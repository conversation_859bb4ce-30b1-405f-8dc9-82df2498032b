#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指纹管理器

用于生成和管理浏览器指纹，防止账号关联检测
"""

import random
import json
import hashlib
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

@dataclass
class BrowserFingerprint:
    """浏览器指纹数据类"""
    user_agent: str
    screen_resolution: Tuple[int, int]
    color_depth: int
    pixel_ratio: float
    timezone: str
    language: str
    platform: str
    hardware_concurrency: int
    memory: int
    webgl_vendor: str
    webgl_renderer: str
    canvas_fingerprint: str
    audio_fingerprint: str
    fonts: List[str]
    plugins: List[str]
    cookies_enabled: bool
    java_enabled: bool
    flash_enabled: bool
    do_not_track: bool
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'user_agent': self.user_agent,
            'screen_resolution': self.screen_resolution,
            'color_depth': self.color_depth,
            'pixel_ratio': self.pixel_ratio,
            'timezone': self.timezone,
            'language': self.language,
            'platform': self.platform,
            'hardware_concurrency': self.hardware_concurrency,
            'memory': self.memory,
            'webgl_vendor': self.webgl_vendor,
            'webgl_renderer': self.webgl_renderer,
            'canvas_fingerprint': self.canvas_fingerprint,
            'audio_fingerprint': self.audio_fingerprint,
            'fonts': self.fonts,
            'plugins': self.plugins,
            'cookies_enabled': self.cookies_enabled,
            'java_enabled': self.java_enabled,
            'flash_enabled': self.flash_enabled,
            'do_not_track': self.do_not_track
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'BrowserFingerprint':
        """从字典创建"""
        return cls(
            user_agent=data['user_agent'],
            screen_resolution=tuple(data['screen_resolution']),
            color_depth=data['color_depth'],
            pixel_ratio=data['pixel_ratio'],
            timezone=data['timezone'],
            language=data['language'],
            platform=data['platform'],
            hardware_concurrency=data['hardware_concurrency'],
            memory=data['memory'],
            webgl_vendor=data['webgl_vendor'],
            webgl_renderer=data['webgl_renderer'],
            canvas_fingerprint=data['canvas_fingerprint'],
            audio_fingerprint=data['audio_fingerprint'],
            fonts=data['fonts'],
            plugins=data['plugins'],
            cookies_enabled=data['cookies_enabled'],
            java_enabled=data['java_enabled'],
            flash_enabled=data['flash_enabled'],
            do_not_track=data['do_not_track']
        )
    
    def get_hash(self) -> str:
        """获取指纹哈希值"""
        fingerprint_str = json.dumps(self.to_dict(), sort_keys=True)
        return hashlib.md5(fingerprint_str.encode()).hexdigest()

class FingerprintManager:
    """指纹管理器"""
    
    def __init__(self, db_manager, bot_logger):
        self.db_manager = db_manager
        self.bot_logger = bot_logger
        self.fingerprint_cache = {}  # account_id -> fingerprint
        
        # 预定义的指纹组件
        self.user_agents = {
            'windows_chrome': [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36'
            ],
            'windows_firefox': [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/******** Firefox/120.0',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/******** Firefox/119.0',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:118.0) Gecko/******** Firefox/118.0'
            ],
            'mac_chrome': [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36'
            ],
            'mac_safari': [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15'
            ]
        }
        
        self.screen_resolutions = [
            (1920, 1080), (1366, 768), (1440, 900), (1536, 864),
            (1280, 720), (1600, 900), (1920, 1200), (2560, 1440),
            (1680, 1050), (1280, 1024), (1024, 768), (1152, 864)
        ]
        
        self.timezones = [
            'America/New_York', 'America/Chicago', 'America/Denver', 'America/Los_Angeles',
            'Europe/London', 'Europe/Paris', 'Europe/Berlin', 'Europe/Rome',
            'Asia/Tokyo', 'Asia/Shanghai', 'Asia/Seoul', 'Asia/Kolkata',
            'Australia/Sydney', 'Australia/Melbourne', 'Pacific/Auckland'
        ]
        
        self.languages = [
            'en-US', 'en-GB', 'en-CA', 'en-AU',
            'es-ES', 'es-MX', 'fr-FR', 'fr-CA',
            'de-DE', 'it-IT', 'pt-BR', 'pt-PT',
            'ru-RU', 'ja-JP', 'ko-KR', 'zh-CN',
            'zh-TW', 'ar-SA', 'hi-IN', 'th-TH'
        ]
        
        self.platforms = [
            'Win32', 'Win64', 'MacIntel', 'Linux x86_64', 'Linux i686'
        ]
        
        self.webgl_vendors = [
            'Google Inc.', 'Mozilla', 'WebKit', 'Microsoft Corporation',
            'Intel Inc.', 'NVIDIA Corporation', 'AMD', 'Apple Inc.'
        ]
        
        self.webgl_renderers = [
            'ANGLE (Intel, Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)',
            'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0, D3D11)',
            'ANGLE (AMD, AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0, D3D11)',
            'Intel Iris OpenGL Engine', 'AMD Radeon Pro 560X OpenGL Engine',
            'NVIDIA GeForce GTX 1080 OpenGL Engine', 'Mesa DRI Intel(R) HD Graphics'
        ]
        
        self.common_fonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
            'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
            'Trebuchet MS', 'Arial Black', 'Impact', 'Lucida Sans Unicode',
            'Tahoma', 'Lucida Console', 'Monaco', 'Courier', 'Bradley Hand',
            'Brush Script MT', 'Luminari', 'Chalkduster'
        ]
        
        self.common_plugins = [
            'Chrome PDF Plugin', 'Chrome PDF Viewer', 'Native Client',
            'Widevine Content Decryption Module', 'Shockwave Flash',
            'Java Deployment Toolkit', 'Java Platform SE'
        ]
    
    def generate_fingerprint(self, account_id: int, 
                           country_code: str = None) -> BrowserFingerprint:
        """生成浏览器指纹"""
        try:
            # 检查缓存
            if account_id in self.fingerprint_cache:
                return self.fingerprint_cache[account_id]
            
            # 根据国家代码选择合适的配置
            if country_code:
                timezone, language = self._get_locale_settings(country_code)
            else:
                timezone = random.choice(self.timezones)
                language = random.choice(self.languages)
            
            # 选择平台和用户代理
            platform = random.choice(self.platforms)
            if platform.startswith('Win'):
                if random.choice([True, False]):
                    user_agent = random.choice(self.user_agents['windows_chrome'])
                else:
                    user_agent = random.choice(self.user_agents['windows_firefox'])
            elif platform == 'MacIntel':
                if random.choice([True, False]):
                    user_agent = random.choice(self.user_agents['mac_chrome'])
                else:
                    user_agent = random.choice(self.user_agents['mac_safari'])
            else:
                user_agent = random.choice(self.user_agents['windows_chrome'])
            
            # 生成屏幕相关属性
            screen_resolution = random.choice(self.screen_resolutions)
            color_depth = random.choice([24, 32])
            pixel_ratio = random.choice([1.0, 1.25, 1.5, 2.0])
            
            # 生成硬件相关属性
            hardware_concurrency = random.choice([2, 4, 6, 8, 12, 16])
            memory = random.choice([2, 4, 8, 16, 32])
            
            # 生成WebGL指纹
            webgl_vendor = random.choice(self.webgl_vendors)
            webgl_renderer = random.choice(self.webgl_renderers)
            
            # 生成Canvas指纹
            canvas_fingerprint = self._generate_canvas_fingerprint()
            
            # 生成音频指纹
            audio_fingerprint = self._generate_audio_fingerprint()
            
            # 选择字体
            font_count = random.randint(15, 25)
            fonts = random.sample(self.common_fonts, min(font_count, len(self.common_fonts)))
            
            # 选择插件
            plugin_count = random.randint(3, 7)
            plugins = random.sample(self.common_plugins, min(plugin_count, len(self.common_plugins)))
            
            # 生成其他属性
            cookies_enabled = random.choice([True, True, True, False])  # 大部分启用
            java_enabled = random.choice([False, False, False, True])   # 大部分禁用
            flash_enabled = random.choice([False, False, True])         # 大部分禁用
            do_not_track = random.choice([False, True, True])           # 随机
            
            fingerprint = BrowserFingerprint(
                user_agent=user_agent,
                screen_resolution=screen_resolution,
                color_depth=color_depth,
                pixel_ratio=pixel_ratio,
                timezone=timezone,
                language=language,
                platform=platform,
                hardware_concurrency=hardware_concurrency,
                memory=memory,
                webgl_vendor=webgl_vendor,
                webgl_renderer=webgl_renderer,
                canvas_fingerprint=canvas_fingerprint,
                audio_fingerprint=audio_fingerprint,
                fonts=fonts,
                plugins=plugins,
                cookies_enabled=cookies_enabled,
                java_enabled=java_enabled,
                flash_enabled=flash_enabled,
                do_not_track=do_not_track
            )
            
            # 缓存指纹
            self.fingerprint_cache[account_id] = fingerprint
            
            # 保存到数据库
            self._save_fingerprint(account_id, fingerprint)
            
            self.bot_logger.log_account_action(
                account_id, 'fingerprint_generated',
                f"生成浏览器指纹，哈希: {fingerprint.get_hash()[:8]}"
            )
            
            return fingerprint
            
        except Exception as e:
            self.bot_logger.log_account_action(
                account_id, 'fingerprint_error',
                f"生成指纹失败: {e}"
            )
            raise
    
    def _get_locale_settings(self, country_code: str) -> Tuple[str, str]:
        """根据国家代码获取时区和语言设置"""
        locale_map = {
            'US': ('America/New_York', 'en-US'),
            'CA': ('America/Toronto', 'en-CA'),
            'GB': ('Europe/London', 'en-GB'),
            'AU': ('Australia/Sydney', 'en-AU'),
            'DE': ('Europe/Berlin', 'de-DE'),
            'FR': ('Europe/Paris', 'fr-FR'),
            'ES': ('Europe/Madrid', 'es-ES'),
            'IT': ('Europe/Rome', 'it-IT'),
            'JP': ('Asia/Tokyo', 'ja-JP'),
            'KR': ('Asia/Seoul', 'ko-KR'),
            'CN': ('Asia/Shanghai', 'zh-CN'),
            'RU': ('Europe/Moscow', 'ru-RU'),
            'BR': ('America/Sao_Paulo', 'pt-BR'),
            'MX': ('America/Mexico_City', 'es-MX'),
            'IN': ('Asia/Kolkata', 'hi-IN')
        }
        
        return locale_map.get(country_code, ('America/New_York', 'en-US'))
    
    def _generate_canvas_fingerprint(self) -> str:
        """生成Canvas指纹"""
        # 生成随机的Canvas指纹标识
        import hashlib
        import time
        
        data = f"{random.random()}_{time.time()}_{random.randint(1000, 9999)}"
        return hashlib.md5(data.encode()).hexdigest()[:16]
    
    def _generate_audio_fingerprint(self) -> str:
        """生成音频指纹"""
        # 生成随机的音频指纹标识
        import hashlib
        import time
        
        data = f"{random.random()}_{time.time()}_{random.randint(1000, 9999)}_audio"
        return hashlib.md5(data.encode()).hexdigest()[:16]
    
    def _save_fingerprint(self, account_id: int, fingerprint: BrowserFingerprint):
        """保存指纹到数据库"""
        try:
            fingerprint_data = json.dumps(fingerprint.to_dict())
            
            # 这里应该保存到数据库
            # self.db_manager.save_fingerprint(account_id, fingerprint_data)
            
        except Exception as e:
            self.bot_logger.log_system_action(
                'fingerprint_save_error',
                f"保存指纹失败: {e}"
            )
    
    def load_fingerprint(self, account_id: int) -> Optional[BrowserFingerprint]:
        """从数据库加载指纹"""
        try:
            # 检查缓存
            if account_id in self.fingerprint_cache:
                return self.fingerprint_cache[account_id]
            
            # 从数据库加载
            # fingerprint_data = self.db_manager.get_fingerprint(account_id)
            # if fingerprint_data:
            #     fingerprint_dict = json.loads(fingerprint_data)
            #     fingerprint = BrowserFingerprint.from_dict(fingerprint_dict)
            #     self.fingerprint_cache[account_id] = fingerprint
            #     return fingerprint
            
            return None
            
        except Exception as e:
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'fingerprint_load_error',
                f"加载指纹失败: {e}"
            )
            return None
    
    def get_or_create_fingerprint(self, account_id: int, 
                                 country_code: str = None) -> BrowserFingerprint:
        """获取或创建指纹"""
        fingerprint = self.load_fingerprint(account_id)
        if fingerprint is None:
            fingerprint = self.generate_fingerprint(account_id, country_code)
        return fingerprint
    
    def update_fingerprint(self, account_id: int, 
                          updates: Dict) -> BrowserFingerprint:
        """更新指纹"""
        fingerprint = self.get_or_create_fingerprint(account_id)
        
        # 更新指纹属性
        fingerprint_dict = fingerprint.to_dict()
        fingerprint_dict.update(updates)
        
        # 创建新指纹
        new_fingerprint = BrowserFingerprint.from_dict(fingerprint_dict)
        
        # 更新缓存
        self.fingerprint_cache[account_id] = new_fingerprint
        
        # 保存到数据库
        self._save_fingerprint(account_id, new_fingerprint)
        
        self.bot_logger.log_account_action(
            account_id, 'fingerprint_updated',
            f"更新浏览器指纹: {list(updates.keys())}"
        )
        
        return new_fingerprint
    
    def delete_fingerprint(self, account_id: int):
        """删除指纹"""
        try:
            # 从缓存删除
            if account_id in self.fingerprint_cache:
                del self.fingerprint_cache[account_id]
            
            # 从数据库删除
            # self.db_manager.delete_fingerprint(account_id)
            
            self.bot_logger.log_account_action(
                account_id, 'fingerprint_deleted', "删除浏览器指纹"
            )
            
        except Exception as e:
            self.bot_logger.log_account_action(
                account_id, 'fingerprint_delete_error',
                f"删除指纹失败: {e}"
            )
    
    def check_fingerprint_similarity(self, account_id1: int, 
                                   account_id2: int) -> float:
        """检查两个指纹的相似度"""
        try:
            fingerprint1 = self.get_or_create_fingerprint(account_id1)
            fingerprint2 = self.get_or_create_fingerprint(account_id2)
            
            # 计算相似度
            similarity_score = 0.0
            total_attributes = 0
            
            fp1_dict = fingerprint1.to_dict()
            fp2_dict = fingerprint2.to_dict()
            
            for key in fp1_dict:
                if key in fp2_dict:
                    total_attributes += 1
                    if fp1_dict[key] == fp2_dict[key]:
                        similarity_score += 1.0
                    elif isinstance(fp1_dict[key], (list, tuple)):
                        # 对于列表类型，计算交集比例
                        set1 = set(fp1_dict[key]) if isinstance(fp1_dict[key], list) else set([fp1_dict[key]])
                        set2 = set(fp2_dict[key]) if isinstance(fp2_dict[key], list) else set([fp2_dict[key]])
                        if set1 and set2:
                            intersection = len(set1.intersection(set2))
                            union = len(set1.union(set2))
                            similarity_score += intersection / union if union > 0 else 0
            
            return similarity_score / total_attributes if total_attributes > 0 else 0.0
            
        except Exception as e:
            self.bot_logger.log_system_action(
                'fingerprint_similarity_error',
                f"计算指纹相似度失败: {e}"
            )
            return 0.0
    
    def get_fingerprint_diversity_report(self, account_ids: List[int]) -> Dict:
        """获取指纹多样性报告"""
        try:
            if not account_ids:
                return {}
            
            fingerprints = []
            for account_id in account_ids:
                fp = self.get_or_create_fingerprint(account_id)
                fingerprints.append(fp)
            
            # 统计各属性的多样性
            diversity_report = {
                'total_accounts': len(account_ids),
                'unique_user_agents': len(set(fp.user_agent for fp in fingerprints)),
                'unique_screen_resolutions': len(set(fp.screen_resolution for fp in fingerprints)),
                'unique_timezones': len(set(fp.timezone for fp in fingerprints)),
                'unique_languages': len(set(fp.language for fp in fingerprints)),
                'unique_platforms': len(set(fp.platform for fp in fingerprints)),
                'unique_webgl_vendors': len(set(fp.webgl_vendor for fp in fingerprints)),
                'unique_canvas_fingerprints': len(set(fp.canvas_fingerprint for fp in fingerprints)),
                'average_similarity': 0.0
            }
            
            # 计算平均相似度
            if len(account_ids) > 1:
                total_similarity = 0.0
                comparison_count = 0
                
                for i in range(len(account_ids)):
                    for j in range(i + 1, len(account_ids)):
                        similarity = self.check_fingerprint_similarity(
                            account_ids[i], account_ids[j]
                        )
                        total_similarity += similarity
                        comparison_count += 1
                
                if comparison_count > 0:
                    diversity_report['average_similarity'] = total_similarity / comparison_count
            
            return diversity_report
            
        except Exception as e:
            self.bot_logger.log_system_action(
                'fingerprint_diversity_error',
                f"生成指纹多样性报告失败: {e}"
            )
            return {}
    
    def optimize_fingerprint_diversity(self, account_ids: List[int]) -> int:
        """优化指纹多样性"""
        try:
            if len(account_ids) < 2:
                return 0
            
            optimized_count = 0
            
            # 检查相似度过高的指纹对
            for i in range(len(account_ids)):
                for j in range(i + 1, len(account_ids)):
                    similarity = self.check_fingerprint_similarity(
                        account_ids[i], account_ids[j]
                    )
                    
                    # 如果相似度过高，重新生成其中一个指纹
                    if similarity > 0.8:  # 相似度阈值
                        account_id = account_ids[j]  # 重新生成第二个账号的指纹
                        
                        # 删除旧指纹
                        self.delete_fingerprint(account_id)
                        
                        # 生成新指纹
                        self.generate_fingerprint(account_id)
                        
                        optimized_count += 1
                        
                        self.bot_logger.log_account_action(
                            account_id, 'fingerprint_optimized',
                            f"优化指纹多样性，原相似度: {similarity:.2f}"
                        )
            
            return optimized_count
            
        except Exception as e:
            self.bot_logger.log_system_action(
                'fingerprint_optimization_error',
                f"优化指纹多样性失败: {e}"
            )
            return 0
    
    def export_fingerprints(self, account_ids: List[int], file_path: str):
        """导出指纹数据"""
        try:
            fingerprints_data = {}
            
            for account_id in account_ids:
                fingerprint = self.get_or_create_fingerprint(account_id)
                fingerprints_data[str(account_id)] = fingerprint.to_dict()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(fingerprints_data, f, ensure_ascii=False, indent=2)
            
            self.bot_logger.log_system_action(
                'fingerprint_export',
                f"导出 {len(account_ids)} 个账号的指纹数据到 {file_path}"
            )
            
        except Exception as e:
            self.bot_logger.log_system_action(
                'fingerprint_export_error',
                f"导出指纹数据失败: {e}"
            )
            raise
    
    def import_fingerprints(self, file_path: str) -> int:
        """导入指纹数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                fingerprints_data = json.load(f)
            
            imported_count = 0
            
            for account_id_str, fingerprint_dict in fingerprints_data.items():
                try:
                    account_id = int(account_id_str)
                    fingerprint = BrowserFingerprint.from_dict(fingerprint_dict)
                    
                    # 更新缓存
                    self.fingerprint_cache[account_id] = fingerprint
                    
                    # 保存到数据库
                    self._save_fingerprint(account_id, fingerprint)
                    
                    imported_count += 1
                    
                except Exception as e:
                    self.bot_logger.log_system_action(
                        'fingerprint_import_item_error',
                        f"导入账号 {account_id_str} 的指纹失败: {e}"
                    )
            
            self.bot_logger.log_system_action(
                'fingerprint_import',
                f"从 {file_path} 导入 {imported_count} 个指纹数据"
            )
            
            return imported_count
            
        except Exception as e:
            self.bot_logger.log_system_action(
                'fingerprint_import_error',
                f"导入指纹数据失败: {e}"
            )
            raise
    
    def clear_cache(self):
        """清空缓存"""
        self.fingerprint_cache.clear()
        
        self.bot_logger.log_system_action(
            'fingerprint_cache_cleared', "清空指纹缓存"
        )
    
    def get_cache_stats(self) -> Dict:
        """获取缓存统计"""
        return {
            'cached_fingerprints': len(self.fingerprint_cache),
            'cache_size_mb': len(str(self.fingerprint_cache)) / 1024 / 1024
        }