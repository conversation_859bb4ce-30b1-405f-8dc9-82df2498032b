#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Twitter登录管理器

用于处理Twitter账号的自动化登录流程
"""

import os
import json
import time
import random
import re
import pyotp
from typing import Dict, Optional, Tuple, List
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import (
    TimeoutException, NoSuchElementException, 
    ElementClickInterceptedException, WebDriverException
)

class TwitterLoginManager:
    """Twitter登录管理器"""
    
    def __init__(self, db_manager, browser_manager, bot_logger):
        self.db_manager = db_manager
        self.browser_manager = browser_manager
        self.bot_logger = bot_logger

        # X URL
        self.login_url = "https://x.com/i/flow/login"
        self.home_url = "https://x.com/home"

        # 登录状态跟踪
        self.logging_in_accounts = set()  # 正在登录的账号ID
        
        # 选择器 - 更新为当前Twitter页面结构
        self.selectors = {
            # 登录页面 - 多个备用选择器
            'username_input': [
                'input[name="text"]',
                'input[autocomplete="username"]',
                'input[data-testid="ocfEnterTextTextInput"]',
                'input[placeholder*="用户名"]',
                'input[placeholder*="邮箱"]',
                'input[placeholder*="电话"]'
            ],
            'password_input': [
                'input[name="password"]',
                'input[type="password"]',
                'input[autocomplete="current-password"]'
            ],

            # 验证页面
            'verification_input': 'input[data-testid="ocfEnterTextTextInput"]',

            # 2FA页面 - 多个备用选择器
            'twofa_input': [
                'input[data-testid="ocfEnterTextTextInput"]',
                'input[placeholder*="code"]',
                'input[placeholder*="验证码"]',
                'input[placeholder*="Enter code"]',
                'input[type="text"][maxlength="6"]',
                'input[autocomplete="one-time-code"]'
            ],

            # 主页检测
            'home_indicator': [
                'div[data-testid="primaryColumn"]',
                'div[data-testid="AppTabBar"]',
                'nav[role="navigation"]'
            ],
            'compose_button': [
                'a[data-testid="SideNav_NewTweet_Button"]',
                'div[data-testid="SideNav_NewTweet_Button"]',
                'a[href="/compose/tweet"]'
            ],

            # 错误信息
            'error_message': [
                'div[data-testid="error"]',
                'div[role="alert"]',
                'span[data-testid="error"]'
            ],
            
            # 安全检查
            'security_check': 'div:has-text("Help us keep your account safe")',
            'phone_verification': 'div:has-text("Enter your phone number")',
            'email_verification': 'div:has-text("Enter your email")',
        }
    
    def login_account(self, account_id: int, headless: bool = True, max_retries: int = 3) -> Tuple[bool, str]:
        """登录Twitter账号 - 增强版本"""
        return self._login_with_retry(account_id, headless, max_retries)
    
    def _login_with_retry(self, account_id: int, headless: bool = True, max_retries: int = 3) -> Tuple[bool, str]:
        """带重试机制的登录"""
        last_error = None
        
        for attempt in range(max_retries):
            try:
                # 标记账号正在登录
                self.logging_in_accounts.add(account_id)
                
                # 如果不是第一次尝试，等待指数退避时间
                if attempt > 0:
                    wait_time = min(2 ** attempt, 30)  # 最大等待30秒
                    self.bot_logger.log_account_action(
                        account_id, "未知", 'login_retry_wait', 
                        f"第{attempt + 1}次登录尝试，等待{wait_time}秒"
                    )
                    time.sleep(wait_time)
                
                # 执行登录逻辑
                success, message = self._execute_login(account_id, headless)
                
                if success:
                    return True, message
                
                # 分析错误类型，决定是否重试
                error_type = self._classify_error(message)
                
                if error_type in ['permanent', 'account_issue']:
                    # 永久性错误，不重试
                    self.bot_logger.log_account_action(
                        account_id, "未知", 'login_permanent_error', 
                        f"检测到永久性错误，停止重试: {message}"
                    )
                    return False, message
                
                last_error = message
                
                if attempt < max_retries - 1:
                    self.bot_logger.log_account_action(
                        account_id, "未知", 'login_retry', 
                        f"第{attempt + 1}次登录失败: {message}，准备重试"
                    )
                    
                    # 清理浏览器状态
                    self._cleanup_browser_state(account_id)
                
            except Exception as e:
                last_error = str(e)
                self.bot_logger.log_account_action(
                    account_id, "未知", 'login_exception', 
                    f"第{attempt + 1}次登录异常: {e}"
                )
                
                # 清理状态
                self.logging_in_accounts.discard(account_id)
                self._cleanup_browser_state(account_id)
                
                if attempt < max_retries - 1:
                    continue
            
            finally:
                # 确保清除登录状态标记
                self.logging_in_accounts.discard(account_id)
        
        return False, f"登录失败，已重试{max_retries}次: {last_error}"
    
    def _execute_login(self, account_id: int, headless: bool = True) -> Tuple[bool, str]:
        """执行登录逻辑"""
        # 获取账号信息
        account = self.db_manager.get_account(account_id)
        if not account:
            return False, "账号不存在"

        # 从字典中获取账号信息
        username = account.get('username', '')
        password = account.get('password', '')
        email = account.get('email', '')
        email_password = account.get('email_password', '')
        twofa_secret = account.get('two_fa', '')
        token = account.get('token', '')

        self.bot_logger.log_account_action(
            account_id, username, 'login_start', f"开始登录账号: {username}"
        )

        # 登录优先级：1. Cookie会话 -> 2. Token -> 3. 用户名密码

        # 首先尝试Cookie会话登录
        self.bot_logger.log_account_action(
            account_id, username, 'login_cookie_attempt', "尝试Cookie会话登录"
        )
        cookie_success, cookie_message = self._login_with_cookies(account_id, headless)
        if cookie_success:
            self.bot_logger.log_account_action(
                account_id, username, 'login_cookie_success', "Cookie会话登录成功"
            )
            return True, "Cookie会话登录成功"

        # Cookie登录失败，尝试Token登录
        if token:
            self.bot_logger.log_account_action(
                account_id, username, 'login_token', f"Cookie登录失败({cookie_message})，尝试Token登录"
            )
            success, message = self._login_with_token(account_id, token, headless)
            if success:
                self.bot_logger.log_account_action(
                    account_id, username, 'login_token_success', "Token登录成功"
                )
                return True, "Token登录成功"
            else:
                self.bot_logger.log_account_action(
                    account_id, username, 'login_token_failed', f"Token登录失败: {message}，回退到用户名密码登录"
                )

        # 如果Cookie和Token都失败，使用用户名密码登录
        self.bot_logger.log_account_action(
            account_id, username, 'login_fallback', "Cookie和Token登录都失败，使用用户名密码登录"
        )

        # 获取或创建浏览器实例（自动处理会话失效）
        driver = self.browser_manager.get_browser(account_id)
        if not driver:
            self.bot_logger.log_account_action(
                account_id, username, 'browser_recreate',
                "浏览器实例不存在或会话失效，正在重新创建"
            )
            driver = self.browser_manager.create_browser(
                account_id=account_id,
                headless=headless
            )
        
        if not driver:
            return False, "创建浏览器失败"
        
        try:
            # 访问登录页面
            self.bot_logger.log_account_action(
                account_id, username, 'login_navigate', "访问登录页面"
            )
            
            driver.get(self.login_url)
            self._wait_for_page_load(driver)
            
            # 检查是否已经登录
            if self._check_logged_in(driver):
                self.bot_logger.log_account_action(
                    account_id, username, 'login_already', "账号已经登录"
                )
                self._update_account_status(account_id, 'logged_in')
                return True, "账号已经登录"
            
            # 输入用户名
            success, message = self._enter_username(driver, username)
            if not success:
                return False, f"输入用户名失败: {message}"
            
            # 处理可能的用户名验证
            if self._check_username_verification(driver):
                success, message = self._handle_username_verification(
                    driver, username, email
                )
                if not success:
                    return False, f"用户名验证失败: {message}"
            
            # 输入密码
            success, message = self._enter_password(driver, password)
            if not success:
                return False, f"输入密码失败: {message}"
            
            # 处理2FA验证
            if self._check_2fa_required(driver):
                if not twofa_secret:
                    return False, "需要2FA验证但未提供密钥"
                
                success, message = self._handle_2fa(driver, twofa_secret)
                if not success:
                    return False, f"2FA验证失败: {message}"
            
            # 处理安全检查
            if self._check_security_verification(driver):
                success, message = self._handle_security_verification(
                    driver, email
                )
                if not success:
                    return False, f"安全验证失败: {message}"
            
            # 检查登录结果
            if self._check_logged_in(driver):
                self.bot_logger.log_account_action(
                    account_id, username, 'login_success', "登录成功"
                )
                self._update_account_status(account_id, 'logged_in')

                # 保存Cookie以供下次使用
                self._save_cookies(driver, account_id)

                return True, "登录成功"
            
            # 检查账号状态
            status_message = self._check_account_status(driver)
            if "suspended" in status_message.lower():
                self._update_account_status(account_id, 'suspended')
                return False, "账号已被封禁"
            elif "locked" in status_message.lower():
                self._update_account_status(account_id, 'locked')
                return False, "账号已被锁定"
            
            return False, f"登录失败: {status_message}"

        finally:
            if headless:
                # 无头模式下关闭浏览器
                self.browser_manager.close_browser(account_id)
    
    def _classify_error(self, error_message: str) -> str:
        """分类错误类型 - 增强版本"""
        error_lower = error_message.lower()
        
        # 永久性错误（不应重试）
        permanent_errors = [
            '账号已被封禁', '账号已被锁定', '账号不存在', '账号被暂停',
            'suspended', 'locked', 'not found', 'banned', 'disabled',
            'account suspended', 'account locked', 'account disabled',
            'your account has been suspended', 'your account is locked'
        ]
        
        # 临时性错误（可以重试）
        temporary_errors = [
            '网络', 'timeout', 'connection', '超时', '连接',
            '浏览器会话失效', 'session', 'webdriver', 'driver',
            'network error', 'connection timeout', 'read timeout',
            'socket timeout', 'connection refused', 'connection reset',
            'temporary failure', 'service unavailable', 'server error',
            'rate limit', '请求过于频繁', 'too many requests'
        ]
        
        # 账号相关问题（可能需要人工干预）
        account_errors = [
            '密码错误', 'wrong password', 'incorrect password',
            '2fa', 'verification', 'verify', '验证',
            'authentication failed', 'login failed',
            'invalid credentials', '凭据无效'
        ]
        
        # 浏览器相关错误（可以重试）
        browser_errors = [
            'chrome', 'chromedriver', 'selenium', 'webdriver',
            'browser', '浏览器', 'element not found', 'no such element',
            'stale element', 'element not interactable', 'javascript error'
        ]
        
        # 按优先级检查错误类型
        for error in permanent_errors:
            if error in error_lower:
                return 'permanent'
        
        for error in account_errors:
            if error in error_lower:
                return 'account_issue'
        
        for error in browser_errors:
            if error in error_lower:
                return 'browser_issue'
        
        for error in temporary_errors:
            if error in error_lower:
                return 'temporary'
        
        return 'unknown'
    
    def _cleanup_browser_state(self, account_id: int):
        """清理浏览器状态 - 增强版本"""
        try:
            self.bot_logger.log_account_action(
                account_id, "未知", 'cleanup_start', "开始清理浏览器状态"
            )
            
            # 1. 安全关闭浏览器
            try:
                driver = self.browser_manager.get_browser(account_id)
                if driver:
                    # 清除所有Cookie和本地存储
                    try:
                        driver.delete_all_cookies()
                        driver.execute_script("window.localStorage.clear();")
                        driver.execute_script("window.sessionStorage.clear();")
                    except Exception as e:
                        self.bot_logger.log_account_action(
                            account_id, "未知", 'cleanup_storage_error', f"清理存储失败: {e}"
                        )
                    
                    # 关闭所有窗口
                    try:
                        for handle in driver.window_handles:
                            driver.switch_to.window(handle)
                            driver.close()
                    except Exception as e:
                        self.bot_logger.log_account_action(
                            account_id, "未知", 'cleanup_windows_error', f"关闭窗口失败: {e}"
                        )
                    
                    # 退出驱动
                    try:
                        driver.quit()
                    except Exception as e:
                        self.bot_logger.log_account_action(
                            account_id, "未知", 'cleanup_quit_error', f"退出驱动失败: {e}"
                        )
            except Exception as e:
                self.bot_logger.log_account_action(
                    account_id, "未知", 'cleanup_driver_error', f"处理驱动失败: {e}"
                )
            
            # 2. 通过浏览器管理器关闭
            try:
                self.browser_manager.close_browser(account_id)
            except Exception as e:
                self.bot_logger.log_account_action(
                    account_id, "未知", 'cleanup_manager_error', f"浏览器管理器关闭失败: {e}"
                )
            
            # 3. 清理Chrome进程（Windows系统）
            try:
                import subprocess
                import platform
                
                if platform.system() == "Windows":
                    # 查找并终止可能残留的Chrome进程
                    try:
                        subprocess.run(
                            ['taskkill', '/f', '/im', 'chrome.exe'], 
                            capture_output=True, 
                            timeout=5
                        )
                        subprocess.run(
                            ['taskkill', '/f', '/im', 'chromedriver.exe'], 
                            capture_output=True, 
                            timeout=5
                        )
                    except subprocess.TimeoutExpired:
                        pass
                    except Exception as e:
                        self.bot_logger.log_account_action(
                            account_id, "未知", 'cleanup_process_error', f"清理进程失败: {e}"
                        )
            except Exception as e:
                self.bot_logger.log_account_action(
                    account_id, "未知", 'cleanup_system_error', f"系统清理失败: {e}"
                )
            
            # 4. 清理临时文件
            self._cleanup_temp_files(account_id)
            
            # 5. 重置浏览器管理器状态
            try:
                if hasattr(self.browser_manager, 'browsers') and account_id in self.browser_manager.browsers:
                    del self.browser_manager.browsers[account_id]
            except Exception as e:
                self.bot_logger.log_account_action(
                    account_id, "未知", 'cleanup_reset_error', f"重置状态失败: {e}"
                )
            
            self.bot_logger.log_account_action(
                account_id, "未知", 'cleanup_complete', "浏览器状态清理完成"
            )
            
        except Exception as e:
            self.bot_logger.log_account_action(
                account_id, "未知", 'cleanup_error', f"清理浏览器状态失败: {e}"
            )
    
    def _cleanup_temp_files(self, account_id: int):
        """清理临时文件"""
        try:
            import os
            import shutil
            import tempfile
            
            # 清理系统临时目录中的Chrome相关文件
            temp_dir = tempfile.gettempdir()
            chrome_temp_patterns = [
                f'scoped_dir*chrome*{account_id}*',
                f'chrome_*{account_id}*',
                f'.com.google.Chrome*{account_id}*'
            ]
            
            for pattern in chrome_temp_patterns:
                try:
                    import glob
                    for temp_path in glob.glob(os.path.join(temp_dir, pattern)):
                        if os.path.exists(temp_path):
                            if os.path.isdir(temp_path):
                                shutil.rmtree(temp_path, ignore_errors=True)
                            else:
                                os.remove(temp_path)
                except Exception:
                    continue
            
            # 清理浏览器配置文件中的缓存
            profile_path = f"browser_profiles/account_{account_id}"
            if os.path.exists(profile_path):
                cache_dirs = ['Default/Cache', 'Default/Code Cache', 'ShaderCache', 'GPUCache']
                for cache_dir in cache_dirs:
                    cache_path = os.path.join(profile_path, cache_dir)
                    if os.path.exists(cache_path):
                        try:
                            shutil.rmtree(cache_path, ignore_errors=True)
                        except Exception:
                            continue
            
        except Exception as e:
            self.bot_logger.log_account_action(
                account_id, "未知", 'cleanup_temp_error', f"清理临时文件失败: {e}"
            )
    
    def _smart_wait_for_element(self, driver, selectors, element_name="元素", 
                                      timeout=5, wait_type="clickable", retry_count=2):
        """智能元素等待方法 - 支持多种等待策略和重试机制"""
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.common.exceptions import TimeoutException, StaleElementReferenceException
        
        # 等待条件映射
        wait_conditions = {
            "clickable": EC.element_to_be_clickable,
            "visible": EC.visibility_of_element_located,
            "present": EC.presence_of_element_located,
            "invisible": EC.invisibility_of_element_located
        }
        
        condition = wait_conditions.get(wait_type, EC.element_to_be_clickable)
        
        for attempt in range(retry_count):
            try:
                # 确保页面已加载
                if attempt == 0:
                    self._wait_for_page_load(driver, timeout=5)
                
                # 尝试每个选择器
                for selector in selectors:
                    try:
                        # 判断选择器类型
                        if selector.startswith("//") or selector.startswith("("):
                            locator = (By.XPATH, selector)
                        else:
                            locator = (By.CSS_SELECTOR, selector)
                        
                        element = WebDriverWait(driver, timeout // len(selectors)).until(
                            condition(locator)
                        )
                        
                        # 验证元素是否真正可用
                        if wait_type == "clickable":
                            # 检查元素是否被遮挡
                            if not self._is_element_interactable(driver, element):
                                continue
                        
                        return element
                        
                    except (TimeoutException, StaleElementReferenceException):
                        continue
                    except Exception as e:
                        self.bot_logger.log_account_action(
                            None, "未知用户", 'element_wait_error',
                            f"等待{element_name}时出错(选择器: {selector}): {e}"
                        )
                        continue
                
                # 如果所有选择器都失败，等待后重试
                if attempt < retry_count - 1:
                    wait_time = random.uniform(1, 3) * (attempt + 1)
                    time.sleep(wait_time)
                    
            except Exception as e:
                self.bot_logger.log_account_action(
                    None, "未知用户", 'element_wait_exception',
                    f"等待{element_name}异常(尝试{attempt + 1}/{retry_count}): {e}"
                )
                
                if attempt < retry_count - 1:
                    time.sleep(random.uniform(2, 4))
        
        return None
    
    def _is_element_interactable(self, driver, element):
        """检查元素是否可交互（未被遮挡）"""
        try:
            # 检查元素是否显示
            if not element.is_displayed():
                return False
            
            # 检查元素是否启用
            if not element.is_enabled():
                return False
            
            # 检查元素是否被其他元素遮挡
            location = element.location
            size = element.size
            center_x = location['x'] + size['width'] // 2
            center_y = location['y'] + size['height'] // 2
            
            # 获取中心点的元素
            center_element = driver.execute_script(
                "return document.elementFromPoint(arguments[0], arguments[1]);",
                center_x, center_y
            )
            
            # 检查中心点元素是否是目标元素或其子元素
            if center_element:
                return driver.execute_script(
                    "return arguments[0].contains(arguments[1]) || arguments[0] === arguments[1];",
                    element, center_element
                )
            
            return True
            
        except Exception:
            return True  # 出错时默认认为可交互
    
    def _check_network_connection(self, driver):
        """检查网络连接状态"""
        try:
            # 检查浏览器网络状态
            online_status = driver.execute_script("return navigator.onLine;")
            if not online_status:
                return False, "浏览器检测到网络离线"
            
            # 尝试访问Twitter的简单端点
            try:
                driver.execute_script(
                    "fetch('https://x.com/favicon.ico', {method: 'HEAD', mode: 'no-cors'})"
                )
                return True, "网络连接正常"
            except Exception:
                return False, "无法连接到Twitter服务器"
                
        except Exception as e:
            return False, f"网络检查失败: {e}"
    
    def _handle_network_error(self, driver, error_msg):
        """处理网络错误"""
        try:
            self.bot_logger.log_account_action(
                None, "未知用户", 'network_error', f"网络错误: {error_msg}"
            )
            
            # 等待网络恢复
            for attempt in range(3):
                time.sleep(random.uniform(5, 10) * (attempt + 1))
                
                is_connected, status = self._check_network_connection(driver)
                if is_connected:
                    self.bot_logger.log_account_action(
                        None, "未知用户", 'network_recovered', "网络连接已恢复"
                    )
                    return True
                
                self.bot_logger.log_account_action(
                    None, "未知用户", 'network_retry', 
                    f"网络重试 {attempt + 1}/3: {status}"
                )
            
            return False
            
        except Exception as e:
            self.bot_logger.log_account_action(
                None, "未知用户", 'network_handler_error', 
                f"网络错误处理失败: {e}"
            )
            return False
    
    def _smart_retry_operation(self, operation_func, operation_name, max_retries=3, 
                              backoff_factor=1.5, *args, **kwargs):
        """智能重试操作 - 支持指数退避和错误分类"""
        last_error = None
        
        for attempt in range(max_retries):
            try:
                result = operation_func(*args, **kwargs)
                if result:  # 操作成功
                    if attempt > 0:  # 重试后成功
                        self.bot_logger.log_account_action(
                            None, "未知用户", 'retry_success',
                            f"{operation_name}在第{attempt + 1}次尝试后成功"
                        )
                    return result
                    
            except Exception as e:
                last_error = e
                error_type = self._classify_error(str(e))
                
                self.bot_logger.log_account_action(
                    None, "未知用户", 'retry_attempt',
                    f"{operation_name}失败(尝试{attempt + 1}/{max_retries}): {e}"
                )
                
                # 永久性错误不重试
                if error_type == 'permanent':
                    self.bot_logger.log_account_action(
                        None, "未知用户", 'permanent_error',
                        f"{operation_name}遇到永久性错误，停止重试"
                    )
                    break
                
                # 网络错误特殊处理
                if 'network' in error_type or 'timeout' in str(e).lower():
                    if hasattr(self, 'driver') and self.driver:
                        if not self._handle_network_error(self.driver, str(e)):
                            break
                
                # 计算退避时间
                if attempt < max_retries - 1:
                    wait_time = random.uniform(1, 3) * (backoff_factor ** attempt)
                    time.sleep(wait_time)
        
        # 所有重试都失败
        self.bot_logger.log_account_action(
            None, "未知用户", 'retry_exhausted',
            f"{operation_name}重试{max_retries}次后仍然失败: {last_error}"
        )
        return False
    
    def _wait_for_page_load(self, driver, timeout: int = 10):
        """等待页面加载 - 增强版本"""
        try:
            # 等待文档加载完成
            WebDriverWait(driver, timeout).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            
            # 等待jQuery加载完成（如果存在）
            try:
                WebDriverWait(driver, 3).until(
                    lambda d: d.execute_script("return typeof jQuery === 'undefined' || jQuery.active === 0")
                )
            except (TimeoutException, Exception):
                pass
            
            # 等待网络请求完成
            try:
                WebDriverWait(driver, 5).until(
                    lambda d: d.execute_script(
                        "return window.performance.getEntriesByType('navigation')[0].loadEventEnd > 0"
                    )
                )
            except (TimeoutException, Exception):
                pass
            
            # 随机延迟模拟人类行为
            time.sleep(random.uniform(1, 3))
            
        except TimeoutException:
            self.bot_logger.log_account_action(
                None, "未知用户", 'page_load_timeout', 
                f"页面加载超时({timeout}秒)，继续执行"
            )
        except Exception as e:
            self.bot_logger.log_account_action(
                None, "未知用户", 'page_load_error', 
                f"页面加载检查失败: {e}"
            )
    
    def _check_logged_in(self, driver) -> bool:
        """检查是否已登录 - 增强版本"""
        try:
            current_url = driver.current_url

            # 如果在登录页面，肯定没有登录
            if "login" in current_url or "flow" in current_url or "i/flow" in current_url:
                return False

            # 多层验证机制
            verification_layers = [
                self._check_url_indicators,
                self._check_dom_elements,
                self._check_navigation_elements
            ]
            
            passed_checks = 0
            for check_func in verification_layers:
                if check_func(driver):
                    passed_checks += 1
            
            # 至少通过2层验证才认为已登录
            is_logged_in = passed_checks >= 2
            
            if is_logged_in:
                self.bot_logger.log_system_action(
                    'login_check_success', f"登录状态验证通过 ({passed_checks}/3 层验证)"
                )
            
            return is_logged_in

        except Exception as e:
            self.bot_logger.log_system_action(
                'check_login_error', f"检查登录状态失败: {e}"
            )
            return False
    
    def _check_url_indicators(self, driver) -> bool:
        """检查URL指示器"""
        try:
            current_url = driver.current_url
            # 检查是否在主页或其他已登录页面
            logged_in_patterns = [
                'x.com/home',
                'x.com/notifications',
                'x.com/messages',
                'x.com/explore',
                'x.com/i/bookmarks'
            ]
            
            return any(pattern in current_url for pattern in logged_in_patterns)
            
        except Exception:
            return False
    
    def _check_dom_elements(self, driver) -> bool:
        """检查DOM元素指示器"""
        try:
            # 主要登录指示元素（优先级高）
            primary_indicators = self.selectors.get('home_indicator', [])
            
            # 检查主要指示元素
            for selector in primary_indicators[:3]:  # 只检查前3个最重要的
                try:
                    WebDriverWait(driver, 4).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    return True
                except TimeoutException:
                    continue
            
            return False
            
        except Exception:
            return False
    
    def _check_navigation_elements(self, driver) -> bool:
        """检查导航元素指示器"""
        try:
            # 导航相关元素
            navigation_indicators = [
                'div[data-testid="SideNav_AccountSwitcher_Button"]',  # 账号切换按钮
                'a[data-testid="AppTabBar_Home_Link"]',  # 主页链接
                'div[data-testid="toolBar"]',  # 工具栏
                'div[aria-label="主页时间线"]',  # 主页时间线
                'div[aria-label="Home timeline"]',  # 英文主页时间线
                'nav[aria-label="主要"]',  # 主导航
                'nav[aria-label="Primary"]'  # 英文主导航
            ]
            
            found_elements = 0
            for selector in navigation_indicators:
                try:
                    WebDriverWait(driver, 2).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    found_elements += 1
                except TimeoutException:
                    continue
            
            # 至少找到2个导航元素才认为通过
            return found_elements >= 2
            
        except Exception:
            return False

    def _login_with_cookies(self, account_id: int, headless: bool = True) -> Tuple[bool, str]:
        """使用保存的Cookie登录"""
        try:
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'cookie_login_start', "开始Cookie登录流程"
            )

            # 获取或创建浏览器实例（自动处理会话失效）
            driver = self.browser_manager.get_browser(account_id)
            if not driver:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'browser_recreate_cookie',
                    "Cookie登录时浏览器实例不存在或会话失效，正在重新创建"
                )
                driver = self.browser_manager.create_browser(
                    account_id=account_id,
                    headless=headless
                )

            if not driver:
                return False, "创建浏览器失败"

            try:
                # 加载保存的Cookie
                if not self._load_cookies(driver, account_id):
                    return False, "没有保存的Cookie或加载失败"

                # 导航到主页
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'cookie_navigate', "使用Cookie导航到主页"
                )
                driver.get("https://x.com/home")
                time.sleep(5)

                # 检查是否登录成功
                if self._check_logged_in(driver):
                    self._update_account_status(account_id, 'logged_in')
                    self.bot_logger.log_account_action(
                        account_id, "未知用户", 'cookie_success', "Cookie登录成功"
                    )
                    return True, "Cookie登录成功"
                else:
                    return False, "Cookie已过期或无效"

            except WebDriverException as e:
                if "invalid session id" in str(e).lower():
                    self.bot_logger.log_account_action(
                        account_id, "未知用户", 'cookie_session_invalid', 
                        f"Cookie登录时浏览器会话失效: {e}"
                    )
                    self.browser_manager.close_browser(account_id)
                    return False, "浏览器会话失效"
                else:
                    self.bot_logger.log_account_action(
                        account_id, "未知用户", 'cookie_webdriver_error', f"Cookie登录WebDriver异常: {e}"
                    )
                    return False, f"浏览器异常: {e}"
            except Exception as e:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'cookie_exception', f"Cookie登录异常: {e}"
                )
                return False, f"Cookie登录异常: {e}"

        except WebDriverException as e:
            if "invalid session id" in str(e).lower():
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'cookie_session_invalid_outer', 
                    f"Cookie登录外层浏览器会话失效: {e}"
                )
                self.browser_manager.close_browser(account_id)
                return False, "浏览器会话失效"
            else:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'cookie_webdriver_error_outer', f"Cookie登录外层WebDriver异常: {e}"
                )
                return False, f"浏览器异常: {e}"
        except Exception as e:
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'cookie_login_error', f"Cookie登录失败: {e}"
            )
            return False, f"Cookie登录失败: {e}"

    def _enter_username(self, driver, username: str) -> Tuple[bool, str]:
        """输入用户名 - 增强版本"""
        try:
            self.bot_logger.log_account_action(
                None, "未知用户", 'login_username', "输入用户名"
            )

            # 智能定位用户名输入框
            username_input = self._find_input_element(
                driver, 
                self.selectors['username_input'],
                "用户名输入框",
                backup_strategies=[
                    "input[name='text']",
                    "input[autocomplete='username']",
                    "input[placeholder*='phone']",
                    "input[placeholder*='email']",
                    "input[placeholder*='username']"
                ]
            )
            
            if not username_input:
                return False, "找不到用户名输入框"

            # 清空输入框并输入用户名
            success = self._safe_input_text(username_input, username, "用户名")
            if not success:
                return False, "输入用户名失败"

            # 智能点击下一步按钮
            success = self._click_next_button(driver, username_input)
            if not success:
                return False, "点击下一步按钮失败"

            # 等待页面响应
            time.sleep(random.uniform(2, 4))

            return True, "用户名输入成功"
            
        except Exception as e:
            return False, f"输入用户名异常: {e}"


    
    def _check_username_verification(self, driver) -> bool:
        """检查是否需要用户名验证"""
        try:
            # 检查是否出现了额外的验证步骤
            verification_elements = driver.find_elements(
                By.XPATH, "//span[contains(text(), 'Enter your phone') or contains(text(), 'Enter your email')]"
            )
            return len(verification_elements) > 0
        except:
            return False
    
    def _handle_username_verification(self, driver, username: str, email: str) -> Tuple[bool, str]:
        """处理用户名验证 - 增强版本"""
        try:
            self.bot_logger.log_account_action(
                None, "未知用户", 'login_verification', "处理用户名验证"
            )
            
            # 智能定位验证输入框
            verification_input = self._find_input_element(
                driver,
                self.selectors['verification_input'],
                "验证输入框",
                backup_strategies=[
                    "input[data-testid='ocfEnterTextTextInput']",
                    "input[name='text']",
                    "input[placeholder*='phone']",
                    "input[placeholder*='email']",
                    "input[autocomplete='username']"
                ],
                timeout=10
            )
            
            if not verification_input:
                return False, "找不到验证输入框"
            
            # 智能选择验证值
            verification_value = self._choose_verification_value(driver, username, email)
            
            # 输入验证值
            success = self._safe_input_text(verification_input, verification_value, "验证信息")
            if not success:
                return False, "输入验证信息失败"
            
            # 智能点击验证按钮
            success = self._click_next_button(driver, verification_input)
            if not success:
                return False, "点击验证按钮失败"
            
            # 等待页面响应
            time.sleep(random.uniform(2, 4))
            
            return True, "用户名验证成功"
            
        except Exception as e:
            return False, f"用户名验证异常: {e}"
    
    def _enter_password(self, driver, password: str) -> Tuple[bool, str]:
        """输入密码 - 增强版本"""
        try:
            self.bot_logger.log_account_action(
                None, "未知用户", 'login_password', "输入密码"
            )

            # 智能定位密码输入框
            password_input = self._find_input_element(
                driver,
                self.selectors['password_input'],
                "密码输入框",
                backup_strategies=[
                    "input[name='password']",
                    "input[type='password']",
                    "input[autocomplete='current-password']",
                    "input[placeholder*='password']",
                    "input[placeholder*='密码']"
                ]
            )
            
            if not password_input:
                return False, "找不到密码输入框"

            # 清空输入框并输入密码
            success = self._safe_input_text(password_input, password, "密码")
            if not success:
                return False, "输入密码失败"

            # 智能点击登录按钮
            success = self._click_login_button(driver, password_input)
            if not success:
                return False, "点击登录按钮失败"

            # 等待页面响应
            time.sleep(random.uniform(3, 5))

            return True, "密码输入成功"

        except Exception as e:
            return False, f"输入密码异常: {e}"
    
    def _find_input_element(self, driver, selectors, element_name, backup_strategies=None, timeout=10):
        """智能定位输入元素 - 使用增强的等待机制"""
        try:
            # 合并主选择器和备用策略
            all_selectors = list(selectors)
            if backup_strategies:
                all_selectors.extend(backup_strategies)
            
            # 使用智能等待方法
            element = self._smart_wait_for_element(
                driver, all_selectors, element_name, 
                timeout=timeout, wait_type="clickable", retry_count=2
            )
            
            if element:
                # 额外验证元素是否为输入框
                tag_name = element.tag_name.lower()
                if tag_name in ['input', 'textarea'] or element.get_attribute('contenteditable'):
                    return element
                else:
                    # 如果不是输入框，尝试查找子元素中的输入框
                    try:
                        child_input = element.find_element(By.CSS_SELECTOR, "input, textarea, [contenteditable]")
                        if child_input:
                            return child_input
                    except:
                        pass
                    
                    return element  # 如果找不到子输入框，返回原元素
            
            return None
            
        except Exception as e:
            self.bot_logger.log_account_action(
                None, "未知用户", 'find_input_error',
                f"查找{element_name}异常: {e}"
            )
            return None
    
    def _safe_input_text(self, element, text, field_name):
        """安全输入文本 - 增强版本"""
        try:
            # 确保元素可交互
            if not self._is_element_interactable(element):
                time.sleep(0.5)
                if not self._is_element_interactable(element):
                    self.bot_logger.log_account_action(
                        None, "未知用户", 'input_error', 
                        f"元素{field_name}不可交互"
                    )
                    return False
            
            # 聚焦元素
            element.click()
            time.sleep(random.uniform(0.1, 0.3))
            
            # 多种方式清空输入框
            element.clear()
            time.sleep(0.1)
            
            # 备用清空方式
            from selenium.webdriver.common.keys import Keys
            element.send_keys(Keys.CONTROL + "a")
            time.sleep(0.05)
            element.send_keys(Keys.DELETE)
            time.sleep(random.uniform(0.2, 0.5))
            
            # 模拟人类输入
            self.browser_manager.simulate_human_typing(element, text)
            time.sleep(random.uniform(0.3, 0.7))
            
            # 验证输入是否成功
            actual_value = element.get_attribute('value') or ''
            if actual_value.strip() != text.strip():
                # 如果输入失败，尝试直接设置值
                self.bot_logger.log_account_action(
                    None, "未知用户", 'input_retry', 
                    f"重试输入{field_name}"
                )
                element.clear()
                time.sleep(0.1)
                element.send_keys(text)
                time.sleep(0.3)
                
                # 再次验证
                final_value = element.get_attribute('value') or ''
                if final_value.strip() != text.strip():
                    self.bot_logger.log_account_action(
                        None, "未知用户", 'input_warning', 
                        f"输入{field_name}可能不完整: 期望'{text}', 实际'{final_value}'"
                    )
            
            return True
            
        except Exception as e:
            self.bot_logger.log_account_action(
                None, "未知用户", 'input_error', 
                f"输入{field_name}失败: {e}"
            )
            return False
    
    def _click_next_button(self, driver, fallback_element=None):
        """智能点击下一步按钮 - 使用增强的等待和重试机制"""
        next_selectors = [
            "//span[text()='Next']/parent::div[@role='button']",
            "//span[text()='下一步']/parent::div[@role='button']",
            "//div[@role='button' and contains(text(), 'Next')]",
            "//button[contains(text(), 'Next')]",
            "//div[@data-testid='LoginForm_Login_Button']",
            "//button[@type='submit']",
            "div[role='button'][data-testid*='next']",
            "button[data-testid*='next']"
        ]
        
        def click_operation():
            # 使用智能等待查找按钮
            button = self._smart_wait_for_element(
                driver, next_selectors, "下一步按钮", 
                timeout=8, wait_type="clickable", retry_count=2
            )
            
            if button:
                self.browser_manager.simulate_human_click(button)
                return True
            
            # 如果找不到按钮，尝试按Enter键
            if fallback_element:
                try:
                    from selenium.webdriver.common.keys import Keys
                    fallback_element.send_keys(Keys.RETURN)
                    return True
                except Exception:
                    pass
            
            return False
        
        # 使用智能重试机制
        return self._smart_retry_operation(
            click_operation, "点击下一步按钮", max_retries=2
        )
    
    def _click_login_button(self, driver, fallback_element=None):
        """智能点击登录按钮 - 使用增强的等待和重试机制"""
        login_selectors = [
            "//span[text()='Log in']/parent::div[@role='button']",
            "//span[text()='登录']/parent::div[@role='button']",
            "//div[@role='button' and contains(text(), 'Log in')]",
            "//button[contains(text(), 'Log in')]",
            "//div[@data-testid='LoginForm_Login_Button']",
            "//button[@type='submit']",
            "div[role='button'][data-testid*='login']",
            "button[data-testid*='login']"
        ]
        
        def click_operation():
            # 使用智能等待查找按钮
            button = self._smart_wait_for_element(
                driver, login_selectors, "登录按钮", 
                timeout=8, wait_type="clickable", retry_count=2
            )
            
            if button:
                self.browser_manager.simulate_human_click(button)
                return True
            
            # 如果找不到按钮，尝试按Enter键
            if fallback_element:
                try:
                    from selenium.webdriver.common.keys import Keys
                    fallback_element.send_keys(Keys.RETURN)
                    return True
                except Exception:
                    pass
            
            return False
        
        # 使用智能重试机制
        return self._smart_retry_operation(
            click_operation, "点击登录按钮", max_retries=2
        )
    
    def _choose_verification_value(self, driver, username, email):
        """智能选择验证值"""
        try:
            # 检查页面提示信息
            page_text = driver.page_source.lower()
            
            # 如果页面提示输入邮箱
            if 'email' in page_text or '邮箱' in page_text:
                return email if email else username
            
            # 如果页面提示输入电话
            elif 'phone' in page_text or '电话' in page_text:
                # 如果用户名是电话号码格式，优先使用
                if username and (username.isdigit() or '+' in username):
                    return username
                return email if email else username
            
            # 默认优先使用邮箱
            return email if email else username
            
        except Exception:
            # 出错时默认使用邮箱
             return email if email else username
    
    def _generate_2fa_code(self, twofa_secret):
        """生成2FA代码（支持重试机制）"""
        try:
            import pyotp
            totp = pyotp.TOTP(twofa_secret)
            
            # 生成当前时间的代码
            current_code = totp.now()
            
            # 验证代码有效性（长度检查）
            if len(current_code) == 6 and current_code.isdigit():
                self.bot_logger.log_account_action(
                    None, "未知用户", '2fa_generation', 
                    f"成功生成2FA代码: {current_code[:2]}****"
                )
                return current_code
            else:
                # 如果代码格式不正确，等待下一个时间窗口
                time.sleep(1)
                next_code = totp.now()
                return next_code if len(next_code) == 6 and next_code.isdigit() else None
                
        except ImportError:
            self.bot_logger.log_account_action(
                None, "未知用户", '2fa_error', 
                "缺少pyotp库，请运行: pip install pyotp"
            )
            return None
        except Exception as e:
            self.bot_logger.log_account_action(
                None, "未知用户", '2fa_error', 
                f"生成2FA代码失败: {e}"
            )
            return None
    
    def _click_verify_button(self, driver, fallback_element=None):
        """智能点击验证按钮 - 使用增强的等待和重试机制"""
        verify_selectors = [
            "//span[text()='Next']/parent::div[@role='button']",
            "//span[text()='下一步']/parent::div[@role='button']",
            "//div[@role='button' and contains(text(), 'Next')]",
            "//button[contains(text(), 'Next')]",
            "//div[@role='button' and contains(text(), 'Verify')]",
            "//button[contains(text(), 'Verify')]",
            "//span[text()='验证']/parent::div[@role='button']",
            "//button[@type='submit']",
            "div[role='button'][data-testid*='verify']",
            "button[data-testid*='verify']",
            "div[role='button'][data-testid*='next']"
        ]
        
        def verify_operation():
            # 使用智能等待查找按钮
            button = self._smart_wait_for_element(
                driver, verify_selectors, "验证按钮", 
                timeout=8, wait_type="clickable", retry_count=2
            )
            
            if button:
                self.browser_manager.simulate_human_click(button)
                return True
            
            # 如果找不到按钮，尝试按Enter键
            if fallback_element:
                try:
                    from selenium.webdriver.common.keys import Keys
                    fallback_element.send_keys(Keys.RETURN)
                    return True
                except Exception:
                    pass
            
            return False
        
        # 使用智能重试机制
        return self._smart_retry_operation(
            verify_operation, "点击验证按钮", max_retries=2
        )
    
    def _check_2fa_verification_result(self, driver):
        """检查2FA验证结果"""
        try:
            # 等待页面变化
            time.sleep(2)
            
            # 检查是否还在2FA页面（验证失败）
            if self._check_2fa_required(driver):
                return False
            
            # 检查是否出现错误信息
            error_indicators = [
                "//span[contains(text(), 'Wrong code')]",
                "//span[contains(text(), 'Invalid code')]",
                "//span[contains(text(), '验证码错误')]",
                "//span[contains(text(), '代码无效')]",
                "//div[contains(@class, 'error')]"
            ]
            
            for indicator in error_indicators:
                try:
                    elements = driver.find_elements(By.XPATH, indicator)
                    if elements:
                        return False
                except:
                    continue
            
            # 检查是否成功进入下一步
            current_url = driver.current_url
            if 'login' not in current_url.lower() or 'home' in current_url.lower():
                return True
            
            return True  # 默认认为成功
            
        except Exception:
            return True  # 出错时默认认为成功
    
    def _check_2fa_required(self, driver) -> bool:
        """检查是否需要2FA验证"""
        try:
            # 检查多种2FA页面标识
            twofa_indicators = [
                "//span[contains(text(), 'Enter your verification code')]",
                "//span[contains(text(), 'Enter your 6-digit code')]",
                "//h1[contains(text(), 'Enter your verification code')]",
                "//div[contains(text(), 'Use your code generator app')]",
                "//span[contains(text(), 'verification code')]",
                "//span[contains(text(), '验证码')]"
            ]

            for indicator in twofa_indicators:
                elements = driver.find_elements(By.XPATH, indicator)
                if len(elements) > 0:
                    print(f"检测到2FA页面: {indicator}")
                    return True

            # 也检查2FA输入框的存在
            for selector in self.selectors['twofa_input']:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if len(elements) > 0:
                        print(f"检测到2FA输入框: {selector}")
                        return True
                except:
                    continue

            return False
        except Exception as e:
            print(f"2FA检测异常: {e}")
            return False
    
    def _handle_2fa(self, driver, twofa_secret: str) -> Tuple[bool, str]:
        """处理2FA验证 - 增强版本"""
        try:
            self.bot_logger.log_account_action(
                None, "未知用户", 'login_2fa', "处理2FA验证"
            )

            # 生成2FA代码（支持重试机制）
            twofa_code = self._generate_2fa_code(twofa_secret)
            if not twofa_code:
                return False, "生成2FA代码失败"

            # 智能定位2FA输入框
            twofa_input = self._find_input_element(
                driver,
                self.selectors['twofa_input'],
                "2FA输入框",
                backup_strategies=[
                    "input[data-testid='ocfEnterTextTextInput']",
                    "input[name='challenge_response']",
                    "input[placeholder*='code']",
                    "input[placeholder*='验证码']",
                    "input[maxlength='6']",
                    "input[maxlength='8']"
                ],
                timeout=10
            )
            
            if not twofa_input:
                return False, "找不到2FA输入框"

            # 安全输入2FA代码
            success = self._safe_input_text(twofa_input, twofa_code, "2FA代码")
            if not success:
                return False, "输入2FA代码失败"

            # 智能点击验证按钮
            success = self._click_verify_button(driver, twofa_input)
            if not success:
                return False, "点击验证按钮失败"

            # 等待验证结果
            time.sleep(random.uniform(3, 5))
            
            # 检查是否验证成功
            if self._check_2fa_verification_result(driver):
                return True, "2FA验证成功"
            else:
                return False, "2FA验证失败，可能代码错误或已过期"

        except Exception as e:
            return False, f"2FA验证异常: {e}"
    
    def _check_security_verification(self, driver) -> bool:
        """检查是否需要安全验证"""
        try:
            # 检查安全验证页面
            security_elements = driver.find_elements(
                By.XPATH, "//span[contains(text(), 'Help us keep your account safe')]"
            )
            return len(security_elements) > 0
        except:
            return False
    
    def _handle_security_verification(self, driver, email: str) -> Tuple[bool, str]:
        """处理安全验证"""
        try:
            self.bot_logger.log_account_action(
                None, "未知用户", 'login_security', "处理安全验证"
            )
            
            # 检查是否需要邮箱验证
            if self._check_email_verification_required(driver):
                if not email:
                    return False, "需要邮箱验证但未提供邮箱"
                
                return self._handle_email_verification(driver, email)
            
            # 检查是否需要手机验证
            if self._check_phone_verification_required(driver):
                return False, "需要手机验证，暂不支持"
            
            return True, "安全验证完成"
            
        except Exception as e:
            return False, f"安全验证异常: {e}"
    
    def _check_email_verification_required(self, driver) -> bool:
        """检查是否需要邮箱验证"""
        try:
            email_elements = driver.find_elements(
                By.XPATH, "//span[contains(text(), 'Enter your email')]"
            )
            return len(email_elements) > 0
        except:
            return False
    
    def _check_phone_verification_required(self, driver) -> bool:
        """检查是否需要手机验证"""
        try:
            phone_elements = driver.find_elements(
                By.XPATH, "//span[contains(text(), 'Enter your phone')]"
            )
            return len(phone_elements) > 0
        except:
            return False
    
    def _handle_email_verification(self, driver, email: str) -> Tuple[bool, str]:
        """处理邮箱验证 - 增强版本"""
        try:
            # 智能定位邮箱输入框
            email_input = self._find_input_element(
                driver,
                ["input[type='email']"],
                "邮箱输入框",
                backup_strategies=[
                    "input[name='email']",
                    "input[autocomplete='email']",
                    "input[placeholder*='email']",
                    "input[placeholder*='邮箱']",
                    "input[data-testid='ocfEnterTextTextInput']"
                ],
                timeout=10
            )
            
            if not email_input:
                return False, "找不到邮箱输入框"
            
            # 安全输入邮箱
            success = self._safe_input_text(email_input, email, "邮箱")
            if not success:
                return False, "输入邮箱失败"
            
            # 智能点击下一步按钮
            success = self._click_next_button(driver, email_input)
            if not success:
                return False, "点击下一步按钮失败"
            
            # 等待页面响应
            time.sleep(random.uniform(3, 5))
            
            return True, "邮箱验证成功"
            
        except Exception as e:
            return False, f"邮箱验证异常: {e}"
    
    def _check_account_status(self, driver) -> str:
        """检查账号状态"""
        try:
            # 检查页面文本
            page_text = driver.page_source.lower()
            
            if "suspended" in page_text:
                return "账号已被封禁"
            elif "locked" in page_text:
                return "账号已被锁定"
            elif "error" in page_text:
                # 查找具体错误信息
                error_elements = driver.find_elements(
                    By.CSS_SELECTOR, self.selectors['error_message']
                )
                if error_elements:
                    return error_elements[0].text
                return "登录错误"
            
            return "未知状态"
            
        except Exception as e:
            return f"检查状态异常: {e}"
    
    def _update_account_status(self, account_id: int, status: str):
        """更新账号状态"""
        try:
            self.db_manager.update_account_status(account_id, status)
            
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'status_updated', f"账号状态更新为: {status}"
            )

        except Exception as e:
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'status_update_error', f"更新状态失败: {e}"
            )
    
    def check_login_status(self, account_id: int, force_check: bool = False) -> Tuple[bool, str]:
        """检查账号登录状态

        Args:
            account_id: 账号ID
            force_check: 是否强制检查（会导航到主页）
        """
        try:
            # 如果账号正在登录过程中，不进行状态检查
            if account_id in self.logging_in_accounts:
                return False, "正在登录中"

            # 获取浏览器实例（自动处理会话失效）
            driver = self.browser_manager.get_browser(account_id)
            if not driver:
                return False, "浏览器实例不存在或会话失效"

            # 检查当前页面的登录状态
            current_url = driver.current_url

            # 如果在登录页面，说明未登录
            if "login" in current_url or "flow" in current_url:
                self._update_account_status(account_id, 'not_logged_in')
                return False, "在登录页面"

            # 如果强制检查或不在Twitter页面，才导航到主页
            if force_check or not any(domain in current_url for domain in ["x.com"]):
                driver.get(self.home_url)
                self._wait_for_page_load(driver)

            # 检查登录状态
            if self._check_logged_in(driver):
                self._update_account_status(account_id, 'logged_in')
                return True, "已登录"
            else:
                self._update_account_status(account_id, 'not_logged_in')
                return False, "未登录"

        except WebDriverException as e:
            # 检查是否是会话失效错误
            if "invalid session id" in str(e).lower():
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'session_expired_status_check',
                    f"检查登录状态时会话失效: {e}"
                )
                # 清理失效的浏览器实例
                self.browser_manager._safe_close_browser(account_id)
                return False, "浏览器会话失效"
            else:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'browser_error_status_check',
                    f"检查登录状态时浏览器异常: {e}"
                )
                return False, f"浏览器异常: {e}"
        except Exception as e:
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'status_check_error', f"检查登录状态失败: {e}"
            )
            return False, f"检查失败: {e}"
    
    def logout_account(self, account_id: int) -> Tuple[bool, str]:
        """登出账号"""
        try:
            # 获取浏览器实例
            driver = self.browser_manager.get_browser(account_id)
            if not driver:
                return False, "浏览器实例不存在"
            
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'logout_start', "开始登出账号"
            )
            
            # 访问主页
            driver.get(self.home_url)
            self._wait_for_page_load(driver)
            
            # 点击用户菜单
            try:
                user_menu = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "div[data-testid='SideNav_AccountSwitcher_Button']"))
                )
                self.browser_manager.simulate_human_click(user_menu)
                
                # 点击登出按钮
                logout_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//span[text()='Log out']/parent::div[@role='menuitem']"))
                )
                self.browser_manager.simulate_human_click(logout_button)
                
                # 确认登出
                confirm_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//span[text()='Log out']/parent::div[@role='button']"))
                )
                self.browser_manager.simulate_human_click(confirm_button)
                
                # 等待登出完成
                time.sleep(random.uniform(2, 4))
                
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'logout_success', "登出成功"
                )
                
                self._update_account_status(account_id, 'not_logged_in')
                return True, "登出成功"
                
            except TimeoutException:
                return False, "找不到登出按钮"
                
        except WebDriverException as e:
            # 检查是否是会话失效错误
            if "invalid session id" in str(e).lower():
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'session_expired_logout',
                    f"登出时会话失效: {e}"
                )
                # 清理失效的浏览器实例
                self.browser_manager._safe_close_browser(account_id)
                return False, "浏览器会话失效"
            else:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'browser_error_logout',
                    f"登出时浏览器异常: {e}"
                )
                return False, f"浏览器异常: {e}"
        except Exception as e:
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'logout_error', f"登出异常: {e}"
            )
            return False, f"登出异常: {e}"
    
    def batch_login(self, account_ids: List[int], 
                   max_concurrent: int = 3) -> Dict[int, Tuple[bool, str]]:
        """批量登录账号"""
        import threading
        from concurrent.futures import ThreadPoolExecutor, as_completed
        
        results = {}
        
        def login_single_account(account_id):
            try:
                return account_id, self.login_account(account_id, headless=True)
            except Exception as e:
                return account_id, (False, f"登录异常: {e}")
        
        self.bot_logger.log_system_action(
            'batch_login_start', f"开始批量登录 {len(account_ids)} 个账号"
        )
        
        with ThreadPoolExecutor(max_workers=max_concurrent) as executor:
            # 提交所有登录任务
            future_to_account = {
                executor.submit(login_single_account, account_id): account_id 
                for account_id in account_ids
            }
            
            # 收集结果
            for future in as_completed(future_to_account):
                account_id, result = future.result()
                results[account_id] = result
                
                success, message = result
                status = "成功" if success else "失败"
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'batch_login_result', f"批量登录{status}: {message}"
                )
        
        # 统计结果
        success_count = sum(1 for success, _ in results.values() if success)
        total_count = len(results)
        
        self.bot_logger.log_system_action(
            'batch_login_complete', 
            f"批量登录完成: {success_count}/{total_count} 成功"
        )
        
        return results
    
    def get_login_statistics(self) -> Dict:
        """获取登录统计"""
        try:
            stats = self.db_manager.get_account_status_stats()
            
            return {
                'total_accounts': stats.get('total', 0),
                'logged_in': stats.get('logged_in', 0),
                'not_logged_in': stats.get('not_logged_in', 0),
                'locked': stats.get('locked', 0),
                'suspended': stats.get('suspended', 0),
                'login_success_rate': (
                    stats.get('logged_in', 0) / stats.get('total', 1) * 100
                    if stats.get('total', 0) > 0 else 0
                )
            }
            
        except Exception as e:
            self.bot_logger.log_system_action(
                'login_stats_error', f"获取登录统计失败: {e}"
            )
            return {}
    
    def refresh_login_status(self, account_ids: List[int]) -> Dict[int, str]:
        """刷新账号登录状态"""
        results = {}
        
        for account_id in account_ids:
            try:
                success, message = self.check_login_status(account_id)
                status = 'logged_in' if success else 'not_logged_in'
                results[account_id] = status
                
            except Exception as e:
                results[account_id] = 'error'
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'status_refresh_error', f"刷新状态失败: {e}"
                )
        
        return results

    def _login_with_token(self, account_id: int, token: str, headless: bool = True) -> Tuple[bool, str]:
        """使用token登录"""
        try:
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'token_login_start', "开始token登录流程"
            )

            # 获取或创建浏览器实例（自动处理会话失效）
            driver = self.browser_manager.get_browser(account_id)
            if not driver:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'browser_recreate_token',
                    "Token登录时浏览器实例不存在或会话失效，正在重新创建"
                )
                driver = self.browser_manager.create_browser(
                    account_id=account_id,
                    headless=headless
                )

            if not driver:
                return False, "创建浏览器失败"

            try:
                # 首先检查是否已有有效的Cookie会话
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'cookie_check', "检查已保存的Cookie会话"
                )

                # 访问Twitter主页检查现有会话
                driver.get("https://x.com/home")
                time.sleep(3)

                # 检查是否已经通过Cookie登录
                if self._check_logged_in(driver):
                    self.bot_logger.log_account_action(
                        account_id, "未知用户", 'cookie_login_success', "Cookie会话有效，已登录"
                    )
                    self._update_account_status(account_id, 'logged_in')
                    return True, "Cookie会话登录成功"

                # Cookie会话无效，使用Token登录
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'token_set', "Cookie无效，使用Token登录"
                )

                # 清除现有Cookie和存储
                driver.delete_all_cookies()
                driver.execute_script("localStorage.clear(); sessionStorage.clear();")

                # 重新访问主页
                driver.get("https://x.com")
                time.sleep(2)

                # 设置认证token
                driver.execute_script(f"""
                    localStorage.setItem('auth_token', '{token}');
                    localStorage.setItem('ct0', '{token}');
                    localStorage.setItem('twid', 'u%3D{token}');
                """)

                # 通过cookie设置token
                try:
                    driver.add_cookie({
                        'name': 'auth_token',
                        'value': token,
                        'domain': '.x.com',
                        'path': '/',
                        'secure': True,
                        'httpOnly': False
                    })

                    driver.add_cookie({
                        'name': 'ct0',
                        'value': token,
                        'domain': '.x.com',
                        'path': '/',
                        'secure': True,
                        'httpOnly': False
                    })

                    # 添加其他必要的认证Cookie
                    driver.add_cookie({
                        'name': 'twid',
                        'value': f'u%3D{token}',
                        'domain': '.x.com',
                        'path': '/',
                        'secure': True,
                        'httpOnly': False
                    })

                except Exception as e:
                    self.bot_logger.log_account_action(
                        account_id, "未知用户", 'token_cookie_error', f"设置cookie失败: {e}"
                    )

                # 导航到主页应用token
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'token_navigate', "导航到主页应用Token"
                )
                driver.get("https://x.com/home")
                time.sleep(5)

                # 检查是否登录成功
                if self._check_logged_in(driver):
                    self._update_account_status(account_id, 'logged_in')
                    self.bot_logger.log_account_action(
                        account_id, "未知用户", 'token_success', "Token登录成功"
                    )

                    # 保存Cookie以供下次使用
                    self._save_cookies(driver, account_id)

                    return True, "Token登录成功"
                else:
                    return False, "Token无效或已过期"

            except WebDriverException as e:
                if "invalid session id" in str(e).lower():
                    self.bot_logger.log_account_action(
                        account_id, "未知用户", 'token_session_invalid', 
                        f"Token登录时浏览器会话失效: {e}"
                    )
                    self.browser_manager.close_browser(account_id)
                    return False, "浏览器会话失效"
                else:
                    self.bot_logger.log_account_action(
                        account_id, "未知用户", 'token_webdriver_error', f"Token登录WebDriver异常: {e}"
                    )
                    return False, f"浏览器异常: {e}"
            except Exception as e:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'token_exception', f"Token登录异常: {e}"
                )
                return False, f"Token登录异常: {e}"

        except WebDriverException as e:
            if "invalid session id" in str(e).lower():
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'token_session_invalid_outer', 
                    f"Token登录外层浏览器会话失效: {e}"
                )
                self.browser_manager.close_browser(account_id)
                return False, "浏览器会话失效"
            else:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'token_webdriver_error_outer', f"Token登录外层WebDriver异常: {e}"
                )
                return False, f"浏览器异常: {e}"
        except Exception as e:
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'token_login_error', f"Token登录失败: {e}"
            )
            return False, f"Token登录失败: {e}"

    def _save_cookies(self, driver, account_id: int):
        """保存Cookie到文件 - 增强版"""
        try:
            cookies = driver.get_cookies()
            if not cookies:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'cookies_empty', "没有Cookie可保存"
                )
                return

            # 过滤重要的认证Cookie
            important_cookies = []
            auth_cookie_names = {'auth_token', 'ct0', 'twid', 'kdt', 'remember_checked_on', 
                               'guest_id', 'personalization_id', 'gt', 'att'}
            
            for cookie in cookies:
                # 保存所有Cookie，但标记重要的认证Cookie
                cookie_data = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie['domain'],
                    'path': cookie['path'],
                    'secure': cookie.get('secure', False),
                    'httpOnly': cookie.get('httpOnly', False),
                    'expiry': cookie.get('expiry'),
                    'sameSite': cookie.get('sameSite', 'Lax'),
                    'is_auth_cookie': cookie['name'] in auth_cookie_names,
                    'saved_at': time.time()  # 添加保存时间戳
                }
                important_cookies.append(cookie_data)

            cookies_dir = os.path.join(os.getcwd(), 'browser_profiles', f'account_{account_id}')
            if not os.path.exists(cookies_dir):
                os.makedirs(cookies_dir)

            cookies_file = os.path.join(cookies_dir, 'cookies.json')
            backup_file = os.path.join(cookies_dir, 'cookies_backup.json')
            
            # 创建备份
            if os.path.exists(cookies_file):
                try:
                    import shutil
                    shutil.copy2(cookies_file, backup_file)
                except Exception:
                    pass

            # 保存Cookie数据
            cookie_data = {
                'cookies': important_cookies,
                'saved_at': time.time(),
                'account_id': account_id,
                'domain': 'x.com',
                'version': '2.0'  # 版本标识
            }
            
            with open(cookies_file, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, indent=2, ensure_ascii=False)

            auth_count = sum(1 for c in important_cookies if c['is_auth_cookie'])
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'cookies_saved', 
                f"已保存 {len(important_cookies)} 个Cookie (其中 {auth_count} 个认证Cookie)"
            )
            
        except Exception as e:
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'cookies_save_error', f"保存Cookie失败: {e}"
            )

    def _load_cookies(self, driver, account_id: int) -> bool:
        """从文件加载Cookie - 增强版"""
        try:
            cookies_dir = os.path.join(os.getcwd(), 'browser_profiles', f'account_{account_id}')
            cookies_file = os.path.join(cookies_dir, 'cookies.json')
            backup_file = os.path.join(cookies_dir, 'cookies_backup.json')
            
            if not os.path.exists(cookies_file):
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'cookies_not_found', "Cookie文件不存在"
                )
                return False

            # 尝试加载主Cookie文件
            cookie_data = None
            try:
                with open(cookies_file, 'r', encoding='utf-8') as f:
                    cookie_data = json.load(f)
            except Exception as e:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'cookies_corrupted', f"主Cookie文件损坏: {e}"
                )
                
                # 尝试加载备份文件
                if os.path.exists(backup_file):
                    try:
                        with open(backup_file, 'r', encoding='utf-8') as f:
                            cookie_data = json.load(f)
                        self.bot_logger.log_account_action(
                            account_id, "未知用户", 'cookies_backup_loaded', "使用备份Cookie文件"
                        )
                    except Exception:
                        return False
                else:
                    return False

            # 处理不同版本的Cookie数据格式
            if isinstance(cookie_data, list):
                # 旧版本格式 (直接是Cookie列表)
                cookies = cookie_data
                saved_at = 0
            elif isinstance(cookie_data, dict) and 'cookies' in cookie_data:
                # 新版本格式
                cookies = cookie_data['cookies']
                saved_at = cookie_data.get('saved_at', 0)
            else:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'cookies_format_error', "Cookie文件格式不正确"
                )
                return False

            # 检查Cookie是否过期 (7天)
            current_time = time.time()
            if saved_at > 0 and (current_time - saved_at) > 7 * 24 * 3600:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'cookies_expired', "Cookie已过期 (超过7天)"
                )
                return False

            # 先访问域名
            driver.get("https://x.com")
            time.sleep(2)

            # 添加Cookie
            loaded_count = 0
            auth_loaded_count = 0
            failed_count = 0
            
            for cookie in cookies:
                try:
                    # 检查Cookie是否有过期时间且已过期
                    if 'expiry' in cookie and cookie['expiry']:
                        if current_time > cookie['expiry']:
                            continue
                    
                    # 准备Cookie数据
                    cookie_to_add = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie['domain'],
                        'path': cookie['path'],
                        'secure': cookie.get('secure', False)
                    }
                    
                    # 添加可选字段
                    if 'httpOnly' in cookie:
                        cookie_to_add['httpOnly'] = cookie['httpOnly']
                    if 'sameSite' in cookie:
                        cookie_to_add['sameSite'] = cookie['sameSite']
                    if 'expiry' in cookie and cookie['expiry']:
                        cookie_to_add['expiry'] = cookie['expiry']
                    
                    driver.add_cookie(cookie_to_add)
                    loaded_count += 1
                    
                    # 统计认证Cookie
                    if cookie.get('is_auth_cookie', False):
                        auth_loaded_count += 1
                        
                except Exception as e:
                    failed_count += 1
                    continue

            if loaded_count == 0:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'cookies_load_failed', "没有成功加载任何Cookie"
                )
                return False

            self.bot_logger.log_account_action(
                account_id, "未知用户", 'cookies_loaded', 
                f"已加载 {loaded_count} 个Cookie (其中 {auth_loaded_count} 个认证Cookie), {failed_count} 个失败"
            )
            
            # 刷新页面以应用Cookie
            driver.refresh()
            time.sleep(2)
            
            return True

        except Exception as e:
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'cookies_load_error', f"加载Cookie失败: {e}"
            )
            return False