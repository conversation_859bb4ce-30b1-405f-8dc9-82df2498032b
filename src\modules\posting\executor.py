#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发帖执行器模块 - Selenium版本
"""

import asyncio
import random
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException

from src.database.connection import get_db_session
from src.database.models import Account, PostingTask, MediaFile
from src.core.browser_manager import get_browser_pool, SeleniumDriverWrapper
from src.core.anti_detection import AntiDetectionEngine
from src.core.cookie_session_manager import CookieSessionManager
from src.utils.logger import LoggerMixin
from src.config.constants import X_URLS, X_SELECTORS, TASK_STATUS
from src.config.settings import get_settings


class LoginManager(LoggerMixin):
    """登录管理器"""

    def __init__(self):
        self.settings = get_settings()
        self.browser_pool = get_browser_pool()
        self.anti_detection = AntiDetectionEngine()
        self.cookie_manager = CookieSessionManager()
    
    async def login_account(self, account: Account) -> bool:
        """
        登录账号

        Args:
            account: 账号对象

        Returns:
            是否登录成功
        """
        max_retries = 3
        driver_wrapper = None
        for attempt in range(max_retries):
            try:
                self.logger.info(f"开始登录账号: {account.username} (尝试 {attempt + 1}/{max_retries})")

                # 获取WebDriver
                if not driver_wrapper:
                    driver_wrapper = await self.browser_pool.get_driver(account)
                    if not driver_wrapper:
                        raise Exception("无法获取WebDriver")

                # 检查WebDriver是否有效
                try:
                    await driver_wrapper.execute_script('return document.readyState')
                except Exception as e:
                    self.logger.warning(f"WebDriver无效，重新获取: {e}")
                    try:
                        await driver_wrapper.close()
                    except Exception:
                        pass
                    driver_wrapper = await self.browser_pool.get_driver(account)
                    if not driver_wrapper:
                        raise Exception("重新获取WebDriver失败")

                # 优先尝试Cookie登录
                self.logger.info(f"尝试Cookie快速登录: {account.username}")
                cookie_login_success = await self.cookie_manager.try_cookie_login(driver_wrapper, account)

                if cookie_login_success:
                    self.logger.info(f"Cookie登录成功: {account.username}")
                    self._update_account_login_status(account, True)
                    # 🔄 Cookie登录成功后更新Cookie
                    await self._save_cookies(driver_wrapper, account)
                    return True

                # Cookie登录失败，检查是否已经通过其他方式登录
                if await self._check_login_status(driver_wrapper):
                    self.logger.info(f"账号已登录: {account.username}")
                    # 保存当前的Cookie以便下次使用
                    await self.cookie_manager.save_login_cookies(driver_wrapper, account)
                    return True

                # 页面预热 - 先访问主页
                try:
                    await driver_wrapper.get('https://x.com')
                    # 等待页面加载完成
                    await driver_wrapper.execute_async(
                        lambda: WebDriverWait(driver_wrapper.driver, 10).until(
                            lambda d: d.execute_script('return document.readyState') == 'complete'
                        )
                    )
                    await asyncio.sleep(random.uniform(1, 2))

                    # 模拟人类行为
                    await self.anti_detection.simulate_human_behavior(driver_wrapper)
                except Exception as e:
                    self.logger.warning(f"页面预热失败: {e}")

                # 导航到登录页面，增加重试机制
                for nav_attempt in range(3):
                    try:
                        self.logger.info(f"导航到登录页面 (尝试 {nav_attempt + 1}/3)")
                        await driver_wrapper.get(X_URLS['LOGIN'])

                        # 快速等待页面加载完成
                        await driver_wrapper.execute_async(
                            lambda: WebDriverWait(driver_wrapper.driver, 15).until(
                                lambda d: d.execute_script('return document.readyState') == 'complete'
                            )
                        )

                        # 人类化等待时间
                        await asyncio.sleep(random.uniform(3, 5))

                        # 验证页面是否正确加载
                        current_url = await driver_wrapper.execute_async(
                            lambda: driver_wrapper.driver.current_url
                        )
                        page_title = await driver_wrapper.execute_async(
                            lambda: driver_wrapper.driver.title
                        )

                        self.logger.info(f"当前页面URL: {current_url}")
                        self.logger.info(f"页面标题: {page_title}")

                        if "login" in current_url:
                            self.logger.info("登录页面加载成功")
                            break
                        elif '注册' in page_title or 'Sign up' in page_title:
                            self.logger.warning("检测到注册页面，尝试查找登录链接")

                            # 查找并点击登录链接
                            login_link_found = False
                            login_link_selectors = [
                                "//a[contains(text(), '登录')]",
                                "//a[contains(text(), 'Log in')]",
                                "//span[contains(text(), '登录')]",
                                "//span[contains(text(), 'Log in')]"
                            ]

                            for selector in login_link_selectors:
                                try:
                                    login_link = await driver_wrapper.execute_async(
                                        lambda: WebDriverWait(driver_wrapper.driver, 5).until(
                                            EC.element_to_be_clickable((By.XPATH, selector))
                                        )
                                    )

                                    await driver_wrapper.execute_async(login_link.click)
                                    await asyncio.sleep(3)

                                    # 检查跳转后的页面
                                    new_url = await driver_wrapper.execute_async(
                                        lambda: driver_wrapper.driver.current_url
                                    )

                                    if "login" in new_url:
                                        self.logger.info(f"成功通过登录链接跳转: {selector}")
                                        login_link_found = True
                                        break

                                except Exception as link_error:
                                    self.logger.debug(f"登录链接点击失败 {selector}: {link_error}")
                                    continue

                            if login_link_found:
                                break
                            else:
                                self.logger.warning("未找到有效的登录链接")
                                if nav_attempt < 2:
                                    await asyncio.sleep(3)
                                    continue
                                else:
                                    raise Exception("无法从注册页面跳转到登录页面")
                        else:
                            self.logger.warning(f"页面URL异常: {current_url}")
                            if nav_attempt < 2:
                                await asyncio.sleep(3)
                                continue
                            else:
                                raise Exception(f"登录页面加载失败，当前URL: {current_url}")

                    except Exception as nav_error:
                        self.logger.warning(f"导航尝试 {nav_attempt + 1} 失败: {nav_error}")
                        if nav_attempt < 2:
                            await asyncio.sleep(5)
                            continue
                        else:
                            raise

                # 再次模拟人类行为
                await self.anti_detection.simulate_human_behavior(driver_wrapper)

                # 等待页面完全加载
                await asyncio.sleep(random.uniform(2, 4))

                # 快速输入用户名
                username_success = False

                try:
                    # 直接查找用户名输入框，减少等待时间
                    wait = WebDriverWait(driver_wrapper.driver, 8)
                    username_input = await driver_wrapper.execute_async(
                        lambda: wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, 'input[name="text"]')))
                    )

                    # 快速输入
                    await driver_wrapper.execute_async(username_input.click)
                    await driver_wrapper.execute_async(username_input.clear)
                    await driver_wrapper.execute_async(
                        lambda: username_input.send_keys(account.username)
                    )

                    username_success = True
                    self.logger.info("用户名输入成功")

                except Exception as e:
                    self.logger.debug(f"主要方法失败，尝试备用方法: {e}")
                    # 备用方法：查找第一个text类型的input
                    try:
                        inputs = await driver_wrapper.execute_async(
                            lambda: driver_wrapper.driver.find_elements(By.TAG_NAME, "input")
                        )

                        for inp in inputs:
                            input_type = await driver_wrapper.execute_async(
                                lambda: inp.get_attribute("type")
                            )
                            if input_type == "text" or input_type is None:
                                await driver_wrapper.execute_async(inp.click)
                                await driver_wrapper.execute_async(inp.clear)
                                await driver_wrapper.execute_async(
                                    lambda: inp.send_keys(account.username)
                                )
                                username_success = True
                                self.logger.info("用户名输入成功（备用方法）")
                                break

                    except Exception as fallback_error:
                        self.logger.error(f"用户名输入失败: {fallback_error}")

                if not username_success:
                    raise Exception("无法找到用户名输入框")

                # 模拟思考时间
                # 模拟人类思考时间
                await asyncio.sleep(random.uniform(2, 4))

                # 快速点击下一步按钮
                next_success = False

                try:
                    # 方法1：直接使用XPath快速查找
                    wait = WebDriverWait(driver_wrapper.driver, 8)
                    element = await driver_wrapper.execute_async(
                        lambda: wait.until(EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), '下一步')]")))
                    )
                    await driver_wrapper.execute_async(element.click)
                    next_success = True
                    self.logger.info("下一步按钮点击成功（XPath方法）")

                except Exception as xpath_error:
                    self.logger.debug(f"XPath方法失败: {xpath_error}")

                    # 方法2：查找button[type="button"]
                    try:
                        buttons = await driver_wrapper.execute_async(
                            lambda: driver_wrapper.driver.find_elements(By.CSS_SELECTOR, 'button[type="button"]')
                        )

                        for button in buttons:
                            button_text = await driver_wrapper.execute_async(
                                lambda: button.text.strip()
                            )

                            if ("下一步" in button_text or "Next" in button_text.lower()) and "注册" not in button_text:
                                await driver_wrapper.execute_async(button.click)
                                next_success = True
                                self.logger.info(f"下一步按钮点击成功: '{button_text}'")
                                break

                    except Exception as button_error:
                        self.logger.debug(f"按钮方法失败: {button_error}")

                if not next_success:
                    raise Exception("无法找到下一步按钮")

                # 人类化等待时间
                await asyncio.sleep(random.uniform(2, 4))

                # 验证页面跳转并等待密码页面加载
                self.logger.info("验证页面跳转并等待密码页面加载...")

                # 等待页面跳转完成
                await asyncio.sleep(random.uniform(4, 6))

                current_url = await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.current_url
                )
                self.logger.debug(f"当前URL: {current_url}")

                # 如果跳转到主页，检查是否已经登录
                if current_url == "https://x.com/" or "/home" in current_url:
                    self.logger.info("页面跳转到主页，检查登录状态...")
                    if await self._check_login_status(driver_wrapper):
                        self.logger.info("检测到已经登录成功")
                        return True
                    else:
                        raise Exception("页面跳转异常，未到达密码输入页面")

                # 等待密码页面加载并模拟人类行为
                await asyncio.sleep(random.uniform(3, 5))

                # 模拟人类行为
                await self.anti_detection.simulate_human_behavior(driver_wrapper)

                # 确保页面加载完成
                await driver_wrapper.execute_async(
                    lambda: WebDriverWait(driver_wrapper.driver, 15).until(
                        lambda d: d.execute_script('return document.readyState') == 'complete'
                    )
                )

                # 等待密码输入框出现并模拟人类行为
                await self.anti_detection.simulate_human_behavior(driver_wrapper)
                
                # 快速输入密码
                password_success = False

                try:
                    # 方法1：直接查找password类型的input
                    wait = WebDriverWait(driver_wrapper.driver, 8)
                    password_input = await driver_wrapper.execute_async(
                        lambda: wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, 'input[type="password"]')))
                    )

                    await driver_wrapper.execute_async(password_input.click)
                    await driver_wrapper.execute_async(password_input.clear)
                    await driver_wrapper.execute_async(
                        lambda: password_input.send_keys(account.password)
                    )
                    password_success = True
                    self.logger.info("密码输入成功")

                except Exception as e:
                    self.logger.debug(f"主要方法失败: {e}")

                    # 方法2：查找所有input，找到password类型的
                    try:
                        inputs = await driver_wrapper.execute_async(
                            lambda: driver_wrapper.driver.find_elements(By.TAG_NAME, "input")
                        )

                        for input_elem in inputs:
                            input_type = await driver_wrapper.execute_async(
                                lambda: input_elem.get_attribute("type")
                            )

                            if input_type == "password":
                                await driver_wrapper.execute_async(input_elem.click)
                                await driver_wrapper.execute_async(input_elem.clear)
                                await driver_wrapper.execute_async(
                                    lambda: input_elem.send_keys(account.password)
                                )
                                password_success = True
                                self.logger.info("密码输入成功（备用方法）")
                                break

                    except Exception as fallback_error:
                        self.logger.warning(f"密码输入失败: {fallback_error}")

                if not password_success:
                    raise Exception("无法找到密码输入框")

                # 模拟人类思考时间
                await asyncio.sleep(random.uniform(2, 4))

                # 快速点击登录按钮
                try:
                    wait = WebDriverWait(driver_wrapper.driver, 8)
                    login_button = await driver_wrapper.execute_async(
                        lambda: wait.until(EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), '登录') or contains(text(), 'Log in')]")))
                    )
                    await driver_wrapper.execute_async(login_button.click)
                    self.logger.info("登录按钮点击成功")
                except Exception as e:
                    self.logger.debug(f"XPath登录按钮失败，尝试备用方法: {e}")
                    await self.anti_detection.safe_click(driver_wrapper, X_SELECTORS['LOGIN_BUTTON'])

                # 等待登录完成 - 给服务器足够的处理时间
                await asyncio.sleep(random.uniform(5, 8))

                # 处理2FA（如果需要）
                if account.two_fa_secret:
                    await self._handle_2fa(driver_wrapper, account.two_fa_secret)

                # 检查登录结果
                if await self._check_login_status(driver_wrapper):
                    # 使用新的Cookie管理器保存cookies
                    await self.cookie_manager.save_login_cookies(driver_wrapper, account)

                    # 更新账号状态
                    self._update_account_login_status(account, True)

                    self.logger.info(f"完整登录成功: {account.username}")
                    return True
                else:
                    self.logger.warning(f"登录失败: {account.username} (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        # 等待一段时间后重试
                        await asyncio.sleep(random.uniform(5, 10))
                        continue
                    else:
                        self._update_account_login_status(account, False)
                        return False
                        
            except Exception as e:
                self.logger.error(f"登录异常: {account.username} (尝试 {attempt + 1}/{max_retries}), {e}")
                if attempt < max_retries - 1:
                    # 等待一段时间后重试
                    await asyncio.sleep(random.uniform(5, 10))
                    continue
                else:
                    self._update_account_login_status(account, False)
                    return False
        
        # 如果所有重试都失败了
        self._update_account_login_status(account, False)
        return False
    
    async def _check_login_status(self, driver_wrapper) -> bool:
        """检查登录状态"""
        try:
            # 尝试访问主页
            await driver_wrapper.get(X_URLS['HOME'])

            # 等待页面加载完成，增加超时时间
            await driver_wrapper.execute_async(
                lambda: WebDriverWait(driver_wrapper.driver, 60).until(
                    lambda d: d.execute_script('return document.readyState') == 'complete'
                )
            )

            # 额外等待，确保页面完全加载
            await asyncio.sleep(3)

            # 检查当前URL，如果包含login说明未登录
            current_url = await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.current_url
            )
            if '/login' in current_url or '/i/flow/login' in current_url:
                self.logger.info("检测到登录页面，账号未登录")
                return False

            # 检查是否存在主页特征元素，使用多个选择器
            selectors_to_try = [
                X_SELECTORS['HOME_TIMELINE'],
                '[data-testid="primaryColumn"]',
                '[role="main"]',
                'main[role="main"]'
            ]

            for selector in selectors_to_try:
                try:
                    wait = WebDriverWait(driver_wrapper.driver, 15)
                    await driver_wrapper.execute_async(
                        lambda: wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    )
                    self.logger.info(f"找到主页元素: {selector}")
                    return True
                except TimeoutException:
                    self.logger.debug(f"未找到元素: {selector}")
                    continue

            # 如果所有选择器都失败，检查页面内容
            try:
                page_text = await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script('return document.body.innerText')
                )
                if 'Home' in page_text or 'Timeline' in page_text or 'What\'s happening' in page_text:
                    self.logger.info("通过页面内容确认已登录")
                    return True
            except Exception as text_error:
                self.logger.debug(f"检查页面内容失败: {text_error}")

            return False

        except TimeoutException:
            self.logger.warning("检查登录状态超时")
            return False
        except Exception as e:
            self.logger.warning(f"检查登录状态异常: {e}")
            return False
    
    async def _handle_2fa(self, driver_wrapper, two_fa_secret: str):
        """处理2FA验证"""
        try:
            # 这里需要实现2FA代码生成逻辑
            # 可以使用pyotp库生成TOTP代码
            import pyotp

            totp = pyotp.TOTP(two_fa_secret)
            code = totp.now()

            # 查找2FA输入框并输入代码
            two_fa_input = await driver_wrapper.find_element(By.CSS_SELECTOR, 'input[name="text"]')
            await driver_wrapper.execute_async(two_fa_input.clear)
            await driver_wrapper.execute_async(two_fa_input.send_keys, code)

            # 点击确认
            confirm_button = await driver_wrapper.find_element(By.CSS_SELECTOR, '[data-testid="ocfEnterTextNextButton"]')
            await driver_wrapper.execute_async(confirm_button.click)

            await asyncio.sleep(random.uniform(2, 4))

        except Exception as e:
            self.logger.warning(f"处理2FA失败: {e}")
    
    async def _save_cookies(self, driver_wrapper, account: Account):
        """保存完整的cookies信息"""
        try:
            # 获取完整的Cookie信息
            cookies = await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.get_cookies()
            )

            if not cookies:
                self.logger.warning(f"没有获取到Cookie: {account.username}")
                return False

            # 过滤和清理Cookie，保持完整格式
            valid_cookies = []
            for cookie in cookies:
                # 只保存X平台相关的Cookie
                if cookie.get('domain') and 'x.com' in cookie.get('domain', ''):
                    # 保存完整的Cookie信息
                    clean_cookie = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie['domain'],
                        'path': cookie.get('path', '/'),
                        'secure': cookie.get('secure', False),
                        'httpOnly': cookie.get('httpOnly', False)
                    }
                    # 添加过期时间（如果有）
                    if 'expiry' in cookie:
                        clean_cookie['expiry'] = cookie['expiry']

                    valid_cookies.append(clean_cookie)

            if not valid_cookies:
                self.logger.warning(f"没有有效的X平台Cookie: {account.username}")
                return False

            # 更新账号cookies
            from src.core.account_manager import AccountManager
            account_manager = AccountManager()
            success = account_manager.update_account_cookies(account.id, valid_cookies)

            if success:
                self.logger.info(f"Cookie保存成功: {account.username}, 共 {len(valid_cookies)} 个")
                return True
            else:
                self.logger.error(f"Cookie保存失败: {account.username}")
                return False

        except Exception as e:
            self.logger.error(f"保存cookies失败: {account.username}, 错误: {e}")
            return False
    
    def _update_account_login_status(self, account: Account, success: bool):
        """更新账号登录状态"""
        session = get_db_session()
        try:
            db_account = session.query(Account).filter(
                Account.id == account.id
            ).first()
            
            if db_account:
                if success:
                    db_account.status = 'logged_in'
                    db_account.last_login_time = datetime.now()
                else:
                    db_account.status = 'not_logged_in'
                
                db_account.updated_at = datetime.now()
                session.commit()
                
        except Exception as e:
            session.rollback()
            self.logger.error(f"更新账号状态失败: {e}")
        finally:
            session.close()


class PostingExecutor(LoggerMixin):
    """发帖执行器 - 优雅版本"""

    def __init__(self):
        self.settings = get_settings()
        self.browser_pool = get_browser_pool()
        self.anti_detection = AntiDetectionEngine()
        self.login_manager = LoginManager()
        self.cookie_manager = CookieSessionManager()

        # 🎯 优雅的选择器配置 - 完全基于成功程序
        self.selectors = {
            # 发帖相关 - 使用成功程序的选择器
            'compose_button': 'a[href="/compose/tweet"]',  # 成功程序的主选择器
            'tweet_textbox': 'div[data-testid="tweetTextarea_0"]',
            'tweet_button': 'button[data-testid="tweetButton"]',
            'media_input': 'input[data-testid="fileInput"]',
            'media_button': 'button[aria-label*="Add photos"]',

            # 验证相关
            'tweet_article': 'article[data-testid="tweet"]',
            'home_timeline': '[data-testid="primaryColumn"]'
        }

        # 🔄 备用选择器 - 完全基于成功程序的配置
        self.fallback_selectors = {
            'compose_button': [
                'a[data-testid="SideNav_NewTweet_Button"]',  # 成功程序的备用1
                'div[data-testid="SideNav_NewTweet_Button"]',  # 成功程序的备用2
                'button[data-testid="SideNav_NewTweet_Button"]',  # 成功程序的备用3
                'a[aria-label*="Tweet"]',  # 成功程序的备用4
                'a[aria-label*="Post"]',  # 成功程序的备用5
                'div[role="button"][aria-label*="Tweet"]',  # 成功程序的备用6
                'button[aria-label*="Tweet"]'  # 成功程序的备用7
            ],
            'tweet_button': [
                'button[data-testid="tweetButtonInline"]',
                'button[aria-label*="Post"]'
            ],
            'media_input': [
                'input[type="file"][accept*="image"]',
                'input[type="file"]'
            ]
        }

    async def _find_element_elegantly(self, driver_wrapper, element_key: str, timeout: int = 8):
        """优雅的元素查找 - 基于成功程序的智能查找"""
        self.logger.info(f"🔍 查找元素: {element_key}")

        try:
            # 首先尝试主要选择器
            main_selector = self.selectors[element_key]
            self.logger.debug(f"尝试主要选择器: {main_selector}")

            elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, main_selector)
            if elements:
                element = elements[0]
                is_displayed = await driver_wrapper.execute_async(lambda: element.is_displayed())
                if is_displayed:
                    self.logger.info(f"✅ 使用主要选择器找到元素: {element_key}")
                    return element
                else:
                    self.logger.debug(f"主要选择器找到元素但不可见: {element_key}")
            else:
                self.logger.debug(f"主要选择器未找到元素: {element_key}")

        except Exception as e:
            self.logger.debug(f"主要选择器异常: {element_key}, {e}")

        # 尝试备用选择器
        if element_key in self.fallback_selectors:
            self.logger.debug(f"尝试 {len(self.fallback_selectors[element_key])} 个备用选择器")
            for i, fallback_selector in enumerate(self.fallback_selectors[element_key]):
                try:
                    self.logger.debug(f"尝试备用选择器{i+1}: {fallback_selector}")
                    elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, fallback_selector)
                    if elements:
                        element = elements[0]
                        is_displayed = await driver_wrapper.execute_async(lambda: element.is_displayed())
                        if is_displayed:
                            self.logger.info(f"✅ 使用备用选择器{i+1}找到元素: {element_key}")
                            return element
                        else:
                            self.logger.debug(f"备用选择器{i+1}找到元素但不可见: {fallback_selector}")
                    else:
                        self.logger.debug(f"备用选择器{i+1}未找到元素: {fallback_selector}")
                except Exception as e:
                    self.logger.debug(f"备用选择器{i+1}异常: {fallback_selector}, {e}")
                    continue

        # 最后尝试：调试页面上的发帖相关元素
        if element_key == 'compose_button':
            await self._debug_compose_elements(driver_wrapper)

        self.logger.error(f"❌ 无法找到元素: {element_key}")
        return None

    async def _debug_compose_elements(self, driver_wrapper):
        """调试页面上的发帖相关元素"""
        try:
            self.logger.info("🔍 调试页面上的发帖相关元素...")

            # 检查各种可能的发帖按钮
            debug_selectors = [
                'a[href="/compose/tweet"]',
                'a[data-testid="SideNav_NewTweet_Button"]',
                'div[data-testid="SideNav_NewTweet_Button"]',
                'button[data-testid="SideNav_NewTweet_Button"]',
                'a[aria-label*="Tweet"]',
                'a[aria-label*="Post"]',
                'button[aria-label*="Tweet"]',
                'button[aria-label*="Post"]',
                'div[role="button"][aria-label*="Tweet"]',
                '[data-testid*="Tweet"]',
                '[data-testid*="NewTweet"]',
                '[aria-label*="Tweet"]'
            ]

            for selector in debug_selectors:
                try:
                    elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        element = elements[0]
                        is_displayed = await driver_wrapper.execute_async(lambda: element.is_displayed())
                        tag_name = await driver_wrapper.execute_async(lambda: element.tag_name)
                        text = await driver_wrapper.execute_async(lambda: element.text or "")
                        aria_label = await driver_wrapper.execute_async(lambda: element.get_attribute('aria-label') or "")

                        self.logger.info(f"🎯 找到元素: {selector}")
                        self.logger.info(f"   标签: {tag_name}, 可见: {is_displayed}")
                        self.logger.info(f"   文本: '{text}', aria-label: '{aria_label}'")

                        if is_displayed:
                            self.logger.info(f"✅ 可用的发帖按钮: {selector}")
                except Exception as e:
                    self.logger.debug(f"调试选择器失败: {selector}, {e}")

        except Exception as e:
            self.logger.error(f"调试发帖元素失败: {e}")

    async def _elegant_click(self, driver_wrapper, element):
        """优雅的点击 - 模拟自然人类行为"""
        try:
            # 滚动到元素位置
            await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            )
            await asyncio.sleep(random.uniform(0.3, 0.6))

            # 移动到元素并点击 - 模拟人类行为
            await driver_wrapper.execute_async(
                lambda: ActionChains(driver_wrapper.driver)
                .move_to_element(element)
                .pause(random.uniform(0.1, 0.3))
                .click()
                .perform()
            )

            # 点击后自然延迟
            await asyncio.sleep(random.uniform(0.2, 0.4))
            return True

        except Exception as e:
            self.logger.error(f"优雅点击失败: {e}")
            return False

    async def execute_post(self, account: Account, content: str, media_files: List = None) -> Dict[str, Any]:
        """
        执行发帖

        Args:
            account: 账号对象
            content: 文本内容
            media_files: 媒体文件列表

        Returns:
            发帖结果字典
        """
        driver_wrapper = None
        try:
            self.logger.info(f"🚀 开始发帖: {account.username}")

            # 🔍 详细记录传入参数
            self.logger.info(f"📝 文案内容: '{content}' (长度: {len(content) if content else 0})")
            self.logger.info(f"📁 媒体文件数量: {len(media_files) if media_files else 0}")
            if media_files:
                for i, media in enumerate(media_files):
                    self.logger.info(f"   媒体 {i+1}: {media.filename} ({media.file_path})")

            # 获取WebDriver
            driver_wrapper = await self.browser_pool.get_driver(account)
            if not driver_wrapper:
                raise Exception("获取WebDriver失败")

            # 确保账号已登录
            if not await self.login_manager.login_account(account):
                raise Exception("账号登录失败")

            # 🎯 使用优雅的发帖流程 - 基于成功程序
            success, message, tweet_id = await self._elegant_compose_tweet(
                driver_wrapper, account, content, media_files
            )

            if success:
                # 更新账号发帖数和Cookie
                self._update_account_post_count(account)
                await self.cookie_manager.save_login_cookies(driver_wrapper, account)

                post_url = f"https://x.com/{account.username}/status/{tweet_id}" if tweet_id else ""
                self.logger.info(f"✅ 发帖成功: {account.username}, URL: {post_url}")
                return {
                    'success': True,
                    'post_url': post_url,
                    'tweet_id': tweet_id
                }
            else:
                self.logger.error(f"❌ 发帖失败: {message}")
                return {
                    'success': False,
                    'error': message
                }
            
        except Exception as e:
            self.logger.error(f"发帖失败: {account.username}, {e}")
            return {
                'success': False,
                'error': str(e)
            }
        finally:
            # WebDriver会在浏览器池中管理，不需要手动关闭
            pass

    async def _elegant_compose_tweet(self, driver_wrapper, account: Account, content: str, media_files: List = None):
        """优雅的发帖流程 - 基于成功程序的简洁实现"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                self.logger.info(f"📝 开始发布推文 (第{attempt + 1}次尝试)")

                # 步骤1: 访问主页
                self.logger.info("🏠 访问主页")
                await driver_wrapper.get("https://x.com/home")
                await asyncio.sleep(3)

                # 步骤2: 查找并点击发帖按钮
                self.logger.info("🔍 查找发帖按钮")
                compose_button = await self._find_element_elegantly(driver_wrapper, 'compose_button')
                if not compose_button:
                    if attempt < max_retries - 1:
                        self.logger.warning("未找到发帖按钮，重试")
                        await asyncio.sleep(3)
                        continue
                    return False, "无法找到发帖按钮", ""

                await self._elegant_click(driver_wrapper, compose_button)
                await asyncio.sleep(2)

                # 步骤3: 查找文本输入框
                self.logger.info("🔍 查找推文输入框")
                tweet_textbox = await self._find_element_elegantly(driver_wrapper, 'tweet_textbox')
                if not tweet_textbox:
                    if attempt < max_retries - 1:
                        self.logger.warning("未找到推文输入框，重试")
                        await asyncio.sleep(3)
                        continue
                    return False, "无法找到推文输入框", ""

                # 步骤4: 输入内容 - 使用我们原有的稳定方法
                if content and content.strip():
                    self.logger.info("📝 输入推文内容")
                    try:
                        await self._input_text_content(driver_wrapper, content)
                        self.logger.info(f"✅ 文案输入完成: {len(content)} 字符")
                    except Exception as e:
                        if attempt < max_retries - 1:
                            self.logger.warning(f"内容输入失败，重试: {e}")
                            await asyncio.sleep(3)
                            continue
                        return False, f"内容输入失败: {e}", ""

                # 步骤5: 上传媒体文件 - 使用融合改进的丝滑方式
                if media_files:
                    self.logger.info("📁 上传媒体文件")
                    success, message = await self._elegant_upload_media(driver_wrapper, media_files)
                    if not success:
                        if attempt < max_retries - 1:
                            self.logger.warning(f"媒体上传失败，重试: {message}")
                            await asyncio.sleep(3)
                            continue
                        return False, f"媒体上传失败: {message}", ""

                # 步骤6: 智能等待媒体上传完成
                if media_files:
                    self.logger.info("⏳ 等待媒体上传完成...")
                    upload_success = await self._wait_for_media_upload_completion(driver_wrapper, len(media_files))
                    if not upload_success:
                        if attempt < max_retries - 1:
                            self.logger.warning("媒体上传未完成，重试")
                            await asyncio.sleep(5)
                            continue
                        return False, "媒体上传未完成", ""

                # 步骤7: 智能查找并验证发布按钮
                self.logger.info("🚀 查找发布按钮")
                tweet_button = await self._find_element_elegantly(driver_wrapper, 'tweet_button')
                if not tweet_button:
                    if attempt < max_retries - 1:
                        self.logger.warning("未找到发布按钮，重试")
                        await asyncio.sleep(3)
                        continue
                    return False, "无法找到发布按钮", ""

                # 智能等待发布按钮可用
                button_ready = await self._wait_for_tweet_button_ready(driver_wrapper, tweet_button)
                if not button_ready:
                    if attempt < max_retries - 1:
                        self.logger.warning("发布按钮未就绪，重试")
                        await asyncio.sleep(3)
                        continue
                    return False, "发布按钮不可用", ""

                # 优雅点击发布按钮
                await self._elegant_click(driver_wrapper, tweet_button)

                # 步骤7: 等待发布完成并获取推文ID
                await asyncio.sleep(random.uniform(3, 5))

                # 检查发布结果
                tweet_id = await self._get_tweet_id_elegantly(driver_wrapper)
                if tweet_id:
                    self.logger.info(f"✅ 推文发布成功 (第{attempt + 1}次尝试)")
                    return True, "推文发布成功", tweet_id
                else:
                    if attempt < max_retries - 1:
                        self.logger.warning("未获取到推文ID，重试")
                        await asyncio.sleep(3)
                        continue
                    return False, "发布成功但未获取到推文ID", ""

            except Exception as e:
                self.logger.error(f"发帖尝试 {attempt + 1} 异常: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(3)
                    continue
                return False, f"发帖异常: {e}", ""

        return False, "发帖最终失败", ""

    async def _elegant_input_content(self, driver_wrapper, textbox, content: str):
        """优雅的内容输入 - 基于成功程序的简洁实现"""
        try:
            # 清空输入框
            await driver_wrapper.execute_async(lambda: textbox.clear())
            await asyncio.sleep(0.5)

            # 点击输入框确保焦点
            await self._elegant_click(driver_wrapper, textbox)
            await asyncio.sleep(0.5)

            # 模拟人类输入
            await self._simulate_human_typing(driver_wrapper, textbox, content)
            await asyncio.sleep(1)

            # 验证内容是否正确输入
            actual_text = await driver_wrapper.execute_async(
                lambda: textbox.get_attribute('textContent') or textbox.get_attribute('value') or ''
            )

            if content.strip() in actual_text.strip():
                self.logger.info(f"✅ 内容输入验证成功: {len(content)} 字符")
                return True
            else:
                self.logger.warning(f"⚠️ 内容输入验证失败: 期望'{content[:20]}...', 实际'{actual_text[:20]}...'")
                return False

        except Exception as e:
            self.logger.error(f"内容输入异常: {e}")
            return False

    async def _simulate_human_typing(self, driver_wrapper, element, text: str):
        """模拟人类打字"""
        try:
            # 使用JavaScript输入，更稳定
            script = """
            arguments[0].focus();
            arguments[0].textContent = arguments[1];

            // 触发输入事件
            var event = new Event('input', { bubbles: true });
            arguments[0].dispatchEvent(event);

            // 触发变化事件
            var changeEvent = new Event('change', { bubbles: true });
            arguments[0].dispatchEvent(changeEvent);
            """

            await driver_wrapper.execute_script(script, element, text)

            # 模拟人类输入后的自然停顿
            await asyncio.sleep(random.uniform(0.5, 1.0))

        except Exception as e:
            self.logger.error(f"模拟打字失败: {e}")
            # 备用方案：直接输入
            await driver_wrapper.execute_async(lambda: element.send_keys(text))

    async def _elegant_upload_media(self, driver_wrapper, media_files: List):
        """优雅的媒体上传 - 基于成功程序的智能实现"""
        try:
            if not media_files:
                return True, "无媒体文件需要上传"

            # 准备文件路径 - 融合改进：确保绝对路径
            file_paths = []
            for media in media_files:
                if hasattr(media, 'file_path'):
                    raw_path = media.file_path
                else:
                    raw_path = str(media)

                # 🔧 关键修复：转换为绝对路径
                if not Path(raw_path).is_absolute():
                    # 相对路径转绝对路径
                    absolute_path = Path.cwd() / raw_path
                    file_paths.append(str(absolute_path.resolve()))
                    self.logger.debug(f"路径转换: {raw_path} → {absolute_path}")
                else:
                    file_paths.append(raw_path)

            # 检查文件是否存在
            for file_path in file_paths:
                if not Path(file_path).exists():
                    return False, f"媒体文件不存在: {file_path}"

            # 🎯 融合改进：简化但有效的媒体输入框查找
            media_input = await self._find_media_input_smartly(driver_wrapper)
            if not media_input:
                return False, "无法找到媒体输入框"

            # 上传文件
            self.logger.info(f"📤 上传 {len(file_paths)} 个文件")
            files_string = '\n'.join(file_paths)
            await driver_wrapper.execute_async(lambda: media_input.send_keys(files_string))

            # 等待上传完成
            await asyncio.sleep(3)

            # 验证上传结果
            success = await self._verify_media_upload_elegantly(driver_wrapper, len(file_paths))
            if success:
                self.logger.info("✅ 媒体文件上传成功")
                return True, "所有媒体文件上传成功"
            else:
                return False, "媒体上传验证失败"

        except Exception as e:
            self.logger.error(f"媒体上传异常: {e}")
            return False, f"媒体上传异常: {e}"

    async def _find_media_input_intelligently(self, driver_wrapper):
        """智能查找媒体输入框 - 基于成功程序的策略"""
        try:
            self.logger.info("🔍 智能查找媒体输入框...")

            # 策略1: 查找data-testid="fileInput"且accept包含媒体类型的输入框
            try:
                file_inputs = await driver_wrapper.find_elements(By.CSS_SELECTOR, '[data-testid="fileInput"]')
                for input_elem in file_inputs:
                    try:
                        input_type = await driver_wrapper.execute_async(lambda: input_elem.get_attribute('type'))
                        if input_type == 'file':
                            accept_attr = await driver_wrapper.execute_async(lambda: input_elem.get_attribute('accept') or '')
                            # 验证这是媒体输入框
                            if any(media_type in accept_attr.lower() for media_type in ['image', 'video', 'jpeg', 'png', 'mp4']):
                                self.logger.info(f"✅ 策略1成功: testid=fileInput, accept={accept_attr}")
                                return input_elem
                    except Exception:
                        continue
            except Exception as e:
                self.logger.debug(f"策略1失败: {e}")

            # 策略2: 使用原有的选择器查找
            try:
                media_input = await self._find_element_elegantly(driver_wrapper, 'media_input')
                if media_input:
                    self.logger.info("✅ 策略2成功: 使用原有选择器")
                    return media_input
            except Exception as e:
                self.logger.debug(f"策略2失败: {e}")

            # 策略3: 查找任何媒体相关的文件输入框
            try:
                file_inputs = await driver_wrapper.find_elements(By.CSS_SELECTOR, 'input[type="file"]')
                for input_elem in file_inputs:
                    try:
                        accept_attr = await driver_wrapper.execute_async(lambda: input_elem.get_attribute('accept') or '')
                        # 检查是否是媒体相关的输入框
                        if any(media_type in accept_attr.lower() for media_type in ['image', 'video', 'jpeg', 'png', 'mp4']):
                            self.logger.info(f"✅ 策略3成功: 通用文件输入框, accept={accept_attr}")
                            return input_elem
                    except Exception:
                        continue
            except Exception as e:
                self.logger.debug(f"策略3失败: {e}")

            # 策略4: 查找任何文件输入框作为最后手段
            try:
                file_inputs = await driver_wrapper.find_elements(By.CSS_SELECTOR, 'input[type="file"]')
                if file_inputs:
                    self.logger.info("✅ 策略4成功: 使用任意文件输入框")
                    return file_inputs[0]
            except Exception as e:
                self.logger.debug(f"策略4失败: {e}")

            self.logger.error("❌ 所有媒体输入框查找策略都失败")
            return None

        except Exception as e:
            self.logger.error(f"智能查找媒体输入框异常: {e}")
            return None

    async def _find_media_input_smartly(self, driver_wrapper):
        """智能但简化的媒体输入框查找 - 融合两种方法的优点"""
        try:
            self.logger.info("🔍 智能查找媒体输入框...")

            # 策略1: 直接使用已经成功的选择器
            try:
                file_inputs = await driver_wrapper.find_elements(By.CSS_SELECTOR, '[data-testid="fileInput"]')
                for input_elem in file_inputs:
                    input_type = await driver_wrapper.execute_async(lambda: input_elem.get_attribute('type'))
                    if input_type == 'file':
                        accept_attr = await driver_wrapper.execute_async(lambda: input_elem.get_attribute('accept') or '')
                        # 简单验证：包含媒体类型
                        if 'image' in accept_attr.lower() or 'video' in accept_attr.lower():
                            self.logger.info(f"✅ 找到媒体输入框: accept={accept_attr}")
                            return input_elem
            except Exception as e:
                self.logger.debug(f"策略1失败: {e}")

            # 策略2: 备用 - 任何文件输入框
            try:
                file_inputs = await driver_wrapper.find_elements(By.CSS_SELECTOR, 'input[type="file"]')
                if file_inputs:
                    self.logger.info("✅ 使用备用文件输入框")
                    return file_inputs[0]
            except Exception as e:
                self.logger.debug(f"策略2失败: {e}")

            self.logger.error("❌ 无法找到媒体输入框")
            return None

        except Exception as e:
            self.logger.error(f"查找媒体输入框异常: {e}")
            return None

    async def _wait_for_media_upload_completion(self, driver_wrapper, expected_count: int) -> bool:
        """
        智能等待媒体上传完成

        Args:
            driver_wrapper: 浏览器包装器
            expected_count: 预期媒体文件数量

        Returns:
            是否上传完成
        """
        try:
            max_wait_time = 60  # 最大等待60秒
            check_interval = 2  # 每2秒检查一次
            elapsed_time = 0

            self.logger.info(f"⏳ 开始等待 {expected_count} 个媒体文件上传完成...")

            while elapsed_time < max_wait_time:
                try:
                    # 检查上传进度指示器
                    progress_indicators = await driver_wrapper.find_elements(
                        By.CSS_SELECTOR,
                        '[role="progressbar"], .progress, [data-testid*="progress"], [aria-label*="uploading"], [aria-label*="processing"]'
                    )

                    if not progress_indicators:
                        # 没有进度指示器，检查媒体预览
                        media_previews = await driver_wrapper.find_elements(
                            By.CSS_SELECTOR,
                            '[data-testid*="media"], [data-testid*="image"], [data-testid*="video"], img[src*="blob:"], video[src*="blob:"]'
                        )

                        if len(media_previews) >= expected_count:
                            # 检查是否有错误指示器
                            error_indicators = await driver_wrapper.find_elements(
                                By.CSS_SELECTOR,
                                '[data-testid*="error"], [aria-label*="error"], [aria-label*="failed"]'
                            )

                            if not error_indicators:
                                self.logger.info(f"✅ 媒体上传完成: 检测到 {len(media_previews)} 个媒体预览")
                                return True

                    # 检查发布按钮状态作为辅助判断
                    try:
                        tweet_button = await driver_wrapper.find_element(By.CSS_SELECTOR, self.selectors['tweet_button'])
                        if tweet_button:
                            is_enabled = await driver_wrapper.execute_async(lambda: tweet_button.is_enabled())
                            button_text = await driver_wrapper.execute_async(lambda: tweet_button.get_attribute('aria-label') or tweet_button.text)

                            # 如果按钮可用且不包含"uploading"等字样，可能上传完成
                            if is_enabled and button_text and 'uploading' not in button_text.lower():
                                self.logger.info("✅ 发布按钮已就绪，媒体可能上传完成")
                                return True
                    except:
                        pass

                    # 等待并更新进度
                    await asyncio.sleep(check_interval)
                    elapsed_time += check_interval

                    if elapsed_time % 10 == 0:  # 每10秒报告一次进度
                        self.logger.info(f"⏳ 等待媒体上传... ({elapsed_time}/{max_wait_time}秒)")

                except Exception as e:
                    self.logger.debug(f"检查上传状态时出错: {e}")
                    await asyncio.sleep(check_interval)
                    elapsed_time += check_interval

            self.logger.warning(f"⚠️ 媒体上传等待超时 ({max_wait_time}秒)")
            return False

        except Exception as e:
            self.logger.error(f"等待媒体上传完成失败: {e}")
            return False

    async def _wait_for_tweet_button_ready(self, driver_wrapper, tweet_button) -> bool:
        """
        智能等待发布按钮就绪

        Args:
            driver_wrapper: 浏览器包装器
            tweet_button: 发布按钮元素

        Returns:
            按钮是否就绪
        """
        try:
            max_wait_time = 30  # 最大等待30秒
            check_interval = 1  # 每1秒检查一次
            elapsed_time = 0

            self.logger.info("⏳ 等待发布按钮就绪...")

            while elapsed_time < max_wait_time:
                try:
                    # 检查按钮是否可用
                    is_enabled = await driver_wrapper.execute_async(lambda: tweet_button.is_enabled())

                    if is_enabled:
                        # 检查按钮文本/标签
                        button_text = await driver_wrapper.execute_async(
                            lambda: tweet_button.get_attribute('aria-label') or tweet_button.text or ''
                        )

                        # 检查是否包含"uploading", "processing"等字样
                        processing_keywords = ['uploading', 'processing', 'loading', '上传中', '处理中']
                        is_processing = any(keyword in button_text.lower() for keyword in processing_keywords)

                        if not is_processing:
                            self.logger.info(f"✅ 发布按钮就绪: {button_text}")
                            return True
                        else:
                            self.logger.debug(f"🔄 按钮仍在处理: {button_text}")
                    else:
                        self.logger.debug("🔄 按钮尚未启用")

                    await asyncio.sleep(check_interval)
                    elapsed_time += check_interval

                    if elapsed_time % 5 == 0:  # 每5秒报告一次进度
                        self.logger.info(f"⏳ 等待按钮就绪... ({elapsed_time}/{max_wait_time}秒)")

                except Exception as e:
                    self.logger.debug(f"检查按钮状态时出错: {e}")
                    await asyncio.sleep(check_interval)
                    elapsed_time += check_interval

            self.logger.warning(f"⚠️ 发布按钮等待超时 ({max_wait_time}秒)")
            return False

        except Exception as e:
            self.logger.error(f"等待发布按钮就绪失败: {e}")
            return False

    async def _verify_media_upload_elegantly(self, driver_wrapper, expected_count: int):
        """优雅的媒体上传验证"""
        try:
            # 等待媒体预览出现
            await asyncio.sleep(2)

            # 查找媒体预览元素
            media_previews = await driver_wrapper.find_elements(
                By.CSS_SELECTOR,
                'div[data-testid="attachments"] img, div[data-testid="attachments"] video'
            )

            actual_count = len(media_previews)
            self.logger.info(f"📊 媒体上传验证: 预期 {expected_count} 个，实际 {actual_count} 个")

            # 宽松验证：至少有一个媒体上传成功
            if actual_count > 0:
                if actual_count >= expected_count:
                    self.logger.info("✅ 所有媒体文件上传成功")
                else:
                    self.logger.info("✅ 部分媒体上传成功，继续发帖流程")
                return True
            else:
                self.logger.warning("❌ 未检测到媒体预览")
                return False

        except Exception as e:
            self.logger.error(f"媒体验证异常: {e}")
            return False

    async def _get_tweet_id_elegantly(self, driver_wrapper):
        """优雅的推文ID获取 - 基于成功程序的方法"""
        try:
            # 等待页面稳定
            await asyncio.sleep(random.uniform(2, 4))

            # 方法1: 从URL获取推文ID
            current_url = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.current_url)
            if '/status/' in current_url:
                tweet_id = current_url.split('/status/')[-1].split('?')[0].split('/')[0]
                if tweet_id.isdigit():
                    self.logger.info(f"📍 从URL获取到推文ID: {tweet_id}")
                    return tweet_id

            # 方法2: 从时间线获取最新推文ID
            tweet_id = await self._extract_tweet_id_from_timeline(driver_wrapper)
            if tweet_id:
                self.logger.info(f"📍 从时间线获取到推文ID: {tweet_id}")
                return tweet_id

            self.logger.warning("⚠️ 无法获取推文ID")
            return ""

        except Exception as e:
            self.logger.error(f"获取推文ID异常: {e}")
            return ""

    async def _extract_tweet_id_from_timeline(self, driver_wrapper):
        """从时间线提取最新推文ID"""
        try:
            # 访问主页
            current_url = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.current_url)
            if "home" not in current_url:
                await driver_wrapper.get("https://x.com/home")
                await asyncio.sleep(3)

            # 查找推文文章
            tweet_articles = await driver_wrapper.find_elements(
                By.CSS_SELECTOR, 'article[data-testid="tweet"]'
            )

            for tweet in tweet_articles[:3]:  # 检查前3个推文
                try:
                    # 查找时间元素
                    time_elements = await driver_wrapper.execute_async(
                        lambda: tweet.find_elements(By.CSS_SELECTOR, 'time')
                    )

                    if time_elements:
                        time_element = time_elements[0]
                        parent_link = await driver_wrapper.execute_async(
                            lambda: time_element.find_element(By.XPATH, './parent::a')
                        )
                        href = await driver_wrapper.execute_async(
                            lambda: parent_link.get_attribute('href')
                        )

                        # 从URL中提取推文ID
                        if href and '/status/' in href:
                            tweet_id = href.split('/status/')[-1].split('?')[0].split('/')[0]
                            if tweet_id.isdigit():
                                return tweet_id

                except Exception as e:
                    self.logger.debug(f"检查推文失败: {e}")
                    continue

            return ""

        except Exception as e:
            self.logger.error(f"从时间线提取推文ID失败: {e}")
            return ""

    async def _input_text_content(self, driver_wrapper, text: str):
        """输入文本内容"""
        try:
            self.logger.info(f"📝 开始输入文案: '{text[:100]}...' (总长度: {len(text)})")

            # 定义多个可能的文本框选择器
            text_area_selectors = [
                '[data-testid="tweetTextarea_0"]',     # 主要选择器
                '[data-testid="tweetTextarea"]',       # 备用选择器1
                '[role="textbox"][aria-label*="Tweet"]', # 备用选择器2
                '[role="textbox"][aria-label*="Post"]', # 新增Post选择器
                '[role="textbox"][placeholder*="What"]', # 备用选择器3
                'div[contenteditable="true"]',         # 备用选择器4
                '.public-DraftEditor-content'           # 备用选择器5
            ]

            text_area = None
            selected_selector = None

            # 尝试找到可用的文本框
            self.logger.info("🔍 正在查找文本输入框...")
            for i, selector in enumerate(text_area_selectors):
                try:
                    self.logger.debug(f"尝试选择器 {i+1}/{len(text_area_selectors)}: {selector}")
                    wait = WebDriverWait(driver_wrapper.driver, 5)
                    text_area = await driver_wrapper.execute_async(
                        lambda: wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, selector)))
                    )
                    selected_selector = selector
                    self.logger.info(f"✅ 找到文本框，使用选择器: {selector}")
                    break
                except Exception as e:
                    self.logger.debug(f"选择器 {selector} 失败: {e}")
                    continue

            if not text_area:
                # 尝试获取页面源码进行调试
                try:
                    page_source = await driver_wrapper.execute_async(
                        lambda: driver_wrapper.driver.page_source
                    )
                    self.logger.error("❌ 未找到文本输入框，页面源码片段:")
                    # 查找可能的文本框元素
                    import re
                    textbox_elements = re.findall(r'<[^>]*(?:textbox|contenteditable|textarea)[^>]*>', page_source)
                    for elem in textbox_elements[:5]:  # 只显示前5个
                        self.logger.error(f"   发现元素: {elem}")
                except Exception:
                    pass
                raise Exception("❌ 未找到文本输入框")

            # 点击文本框
            self.logger.info("🖱️ 点击文本框...")
            await driver_wrapper.execute_async(text_area.click)
            await asyncio.sleep(random.uniform(0.5, 1.0))

            # 🧹 温和的清空操作 - 避免触发异常检测
            self.logger.info("🧹 清空现有内容...")
            try:
                # 🎯 简化的清空方法 - 只使用标准操作
                # 方法1: 全选+删除（模拟用户操作）
                await driver_wrapper.execute_async(
                    lambda: ActionChains(driver_wrapper.driver)
                    .key_down(Keys.CONTROL)
                    .send_keys('a')
                    .key_up(Keys.CONTROL)
                    .send_keys(Keys.DELETE)
                    .perform()
                )
                await asyncio.sleep(0.3)

                # 验证清空结果
                current_text = await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script(
                        "return arguments[0].value || arguments[0].textContent || arguments[0].innerText || '';",
                        text_area
                    )
                )

                if current_text.strip():
                    self.logger.warning(f"⚠️ 清空后仍有残留文本: '{current_text[:30]}...'")
                    # 🔧 备用清空方法 - 只在必要时使用
                    await driver_wrapper.execute_async(text_area.clear)
                    await asyncio.sleep(0.2)
                else:
                    self.logger.info("✅ 文本框已清空")

            except Exception as clear_error:
                self.logger.warning(f"清空内容时出错: {clear_error}")
                # 最简单的清空方法
                try:
                    await driver_wrapper.execute_async(text_area.clear)
                except:
                    pass

            # 模拟人类打字
            self.logger.info(f"⌨️ 开始输入文案: '{text[:50]}...'")
            await self.anti_detection.safe_type(driver_wrapper, selected_selector, text)

            # 🔍 简化验证 - 避免重复输入
            await asyncio.sleep(random.uniform(0.5, 1.0))  # 减少等待时间
            self.logger.info("🔍 验证文案输入结果...")

            # 检查文本是否真正输入成功
            try:
                actual_text = await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script(
                        'return arguments[0].value || arguments[0].textContent || arguments[0].innerText || "";',
                        text_area
                    )
                )

                self.logger.info(f"📋 实际输入的文本: '{actual_text}' (长度: {len(actual_text) if actual_text else 0})")

                # 🎯 更宽松的验证条件 - 避免误判导致重复输入
                if actual_text and (
                    text.strip() in actual_text or
                    actual_text.strip() in text or
                    len(actual_text.strip()) > 0
                ):
                    self.logger.info(f"✅ 文案输入验证成功: {len(actual_text)} 字符")

                    # 🎭 自然的输入完成处理 - 模拟用户完成输入的行为
                    await self._natural_input_completion(driver_wrapper, text_area)
                else:
                    self.logger.warning(f"⚠️ 文案输入可能失败，期望: '{text[:50]}...', 实际: '{actual_text}'")
                    # 🚫 移除备用输入 - 避免重复输入导致重叠
                    # self.logger.warning("🔧 尝试备用输入方法...")
                    # await self._force_text_input(driver_wrapper, text_area, text)

            except Exception as verify_error:
                self.logger.warning(f"❌ 文案验证失败: {verify_error}")
                # 🚫 移除备用输入 - 避免重复输入
                # self.logger.warning("🔧 使用备用输入方法...")
                # await self._force_text_input(driver_wrapper, text_area, text)
            
        except Exception as e:
            self.logger.error(f"输入文本内容失败: {e}")
            raise

    async def _natural_input_completion(self, driver_wrapper, element):
        """🎭 自然的输入完成处理 - 模拟用户完成输入后的自然行为"""
        try:
            self.logger.info("🎭 执行自然的输入完成处理...")

            # 模拟用户完成输入后的自然行为
            await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script("""
                    const element = arguments[0];

                    // 确保焦点在元素上
                    element.focus();

                    // 模拟用户按End键移动到文本末尾
                    const endEvent = new KeyboardEvent('keydown', {
                        key: 'End',
                        code: 'End',
                        bubbles: true,
                        cancelable: true
                    });
                    element.dispatchEvent(endEvent);

                    // 模拟用户短暂停顿（思考）
                    setTimeout(() => {
                        // 轻微的光标移动，模拟用户检查文本
                        if (element.setSelectionRange && element.value) {
                            const length = element.value.length;
                            element.setSelectionRange(length, length);
                        }
                    }, 200);
                """, element)
            )

            # 短暂等待，模拟用户检查输入的文本
            await asyncio.sleep(random.uniform(0.8, 1.5))

            # 模拟用户点击其他地方然后回来（自然的焦点切换）
            await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script("""
                    const element = arguments[0];

                    // 短暂失去焦点
                    element.blur();

                    // 然后重新获得焦点
                    setTimeout(() => {
                        element.focus();
                    }, 300);
                """, element)
            )

            await asyncio.sleep(0.5)
            self.logger.info("✅ 自然输入完成处理完毕")

        except Exception as e:
            self.logger.warning(f"自然输入完成处理失败: {e}")

    async def _force_ui_refresh(self, driver_wrapper, element):
        """强制刷新界面显示，确保文本在UI中可见"""
        try:
            self.logger.info("🔄 强制刷新界面显示...")

            await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script("""
                    const element = arguments[0];

                    // 模拟用户交互来触发界面更新
                    element.click();
                    element.focus();

                    // 触发键盘事件来刷新显示
                    const events = ['keydown', 'keyup', 'input', 'change'];
                    events.forEach(eventType => {
                        const event = new KeyboardEvent(eventType, {
                            key: 'End',
                            code: 'End',
                            bubbles: true,
                            cancelable: true
                        });
                        element.dispatchEvent(event);
                    });

                    // 模拟光标移动到末尾
                    if (element.setSelectionRange) {
                        const length = element.value ? element.value.length : 0;
                        element.setSelectionRange(length, length);
                    }

                    // 强制重新渲染
                    element.blur();
                    setTimeout(() => element.focus(), 100);
                """, element)
            )

            await asyncio.sleep(0.5)
            self.logger.info("✅ 界面刷新完成")

        except Exception as e:
            self.logger.warning(f"界面刷新失败: {e}")

    async def _force_text_input(self, driver_wrapper, element, text: str):
        """强力文本输入 - 多种方法确保输入成功"""
        try:
            self.logger.info("使用强力文本输入方法")

            # 首先彻底清空内容
            try:
                await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script("""
                        arguments[0].value = '';
                        arguments[0].textContent = '';
                        arguments[0].innerText = '';
                        arguments[0].innerHTML = '';
                        // 触发清空事件
                        arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                        arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                    """, element)
                )
                await asyncio.sleep(0.5)  # 等待清空完成
                self.logger.debug("强力清空完成")
            except Exception as clear_error:
                self.logger.warning(f"强力清空失败: {clear_error}")

            # 方法1: 增强的JavaScript输入
            try:
                result = await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script("""
                        const element = arguments[0];
                        const text = arguments[1];

                        // 确保元素获得焦点
                        element.focus();

                        // 设置文本内容到所有可能的属性
                        element.value = text;
                        element.textContent = text;
                        element.innerText = text;

                        // 对于contenteditable元素
                        if (element.contentEditable === 'true' || element.getAttribute('contenteditable') === 'true') {
                            element.innerHTML = text.replace(/\\n/g, '<br>');
                        }

                        // 触发所有必要的事件
                        const events = ['input', 'change', 'keyup', 'keydown', 'focus', 'blur'];
                        events.forEach(eventType => {
                            const event = new Event(eventType, {
                                bubbles: true,
                                cancelable: true
                            });
                            element.dispatchEvent(event);
                        });

                        // 特殊的InputEvent用于现代浏览器
                        const inputEvent = new InputEvent('input', {
                            bubbles: true,
                            cancelable: true,
                            inputType: 'insertText',
                            data: text
                        });
                        element.dispatchEvent(inputEvent);

                        // 强制界面更新
                        element.blur();
                        element.focus();

                        return element.value || element.textContent || element.innerText;
                    """, element, text)
                )
                self.logger.debug(f"方法1: 增强JavaScript输入完成，返回: {result}")
            except Exception as e1:
                self.logger.debug(f"方法1失败: {e1}")

            # 方法2: 设置innerText
            try:
                await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script(
                        "arguments[0].innerText = arguments[1]; arguments[0].dispatchEvent(new Event('input', {bubbles: true}));",
                        element, text
                    )
                )
                self.logger.debug("方法2: innerText设置完成")
            except Exception as e2:
                self.logger.debug(f"方法2失败: {e2}")

            # 方法3: 设置textContent
            try:
                await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script(
                        "arguments[0].textContent = arguments[1]; arguments[0].dispatchEvent(new Event('input', {bubbles: true}));",
                        element, text
                    )
                )
                self.logger.debug("方法3: textContent设置完成")
            except Exception as e3:
                self.logger.debug(f"方法3失败: {e3}")

            # 方法4: 模拟键盘输入
            try:
                await driver_wrapper.execute_async(element.clear)
                await driver_wrapper.execute_async(element.click)
                await asyncio.sleep(0.5)

                # 逐字符输入
                for char in text:
                    await driver_wrapper.execute_async(element.send_keys, char)
                    await asyncio.sleep(0.1)

                self.logger.debug("方法4: 键盘输入完成")
            except Exception as e4:
                self.logger.debug(f"方法4失败: {e4}")

            # 触发所有可能的事件
            try:
                await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script("""
                        var element = arguments[0];
                        var events = ['input', 'change', 'keyup', 'keydown', 'focus', 'blur'];
                        events.forEach(function(eventType) {
                            try {
                                element.dispatchEvent(new Event(eventType, {bubbles: true}));
                            } catch(e) {}
                        });
                    """, element)
                )
                self.logger.debug("事件触发完成")
            except Exception as e5:
                self.logger.debug(f"事件触发失败: {e5}")

            # 最终验证
            await asyncio.sleep(1)
            try:
                final_text = await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script(
                        'return arguments[0].value || arguments[0].innerText || arguments[0].textContent;',
                        element
                    )
                )

                if final_text and text.strip() in final_text:
                    self.logger.info(f"✅ 强力输入成功: {final_text}")
                    # 强制刷新界面显示
                    await self._force_ui_refresh(driver_wrapper, element)
                else:
                    self.logger.error(f"❌ 强力输入仍然失败: 预期'{text}', 实际'{final_text}'")

            except Exception as verify_error:
                self.logger.error(f"强力输入验证失败: {verify_error}")

        except Exception as e:
            self.logger.error(f"强力文本输入失败: {e}")
    
    async def _upload_media_files(self, driver_wrapper, media_files: List[MediaFile]):
        """上传媒体文件 - 支持批量上传"""
        try:
            if not media_files:
                self.logger.info("📁 没有媒体文件需要上传")
                return

            self.logger.info(f"📁 开始处理 {len(media_files)} 个媒体文件")

            # 验证文件存在性
            valid_files = []
            for i, media_file in enumerate(media_files):
                self.logger.info(f"📄 检查媒体文件 {i+1}: {media_file.filename}")
                self.logger.info(f"   路径: {media_file.file_path}")

                if not Path(media_file.file_path).exists():
                    self.logger.warning(f"❌ 媒体文件不存在: {media_file.file_path}")
                    continue

                file_size = Path(media_file.file_path).stat().st_size
                self.logger.info(f"✅ 文件有效，大小: {file_size} 字节")
                valid_files.append(media_file)

            if not valid_files:
                self.logger.warning("❌ 没有有效的媒体文件可上传")
                return

            self.logger.info(f"📁 准备上传 {len(valid_files)} 个有效媒体文件")

            # 查找媒体上传按钮
            self.logger.info("🔍 正在查找媒体上传按钮...")
            media_button_selectors = X_SELECTORS['MEDIA_BUTTON'].split(', ')
            media_button = None

            for i, selector in enumerate(media_button_selectors):
                try:
                    self.logger.debug(f"尝试媒体按钮选择器 {i+1}/{len(media_button_selectors)}: {selector}")
                    wait = WebDriverWait(driver_wrapper.driver, 5)
                    button = await driver_wrapper.execute_async(
                        lambda: wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                    )
                    media_button = button
                    self.logger.info(f"✅ 找到媒体按钮: {selector}")
                    break
                except Exception as e:
                    self.logger.debug(f"媒体按钮选择器 {selector} 失败: {e}")
                    continue

            if not media_button:
                self.logger.error(f"❌ 未找到媒体上传按钮，尝试的选择器: {media_button_selectors}")
                # 尝试获取页面源码进行调试
                try:
                    page_source = await driver_wrapper.execute_async(
                        lambda: driver_wrapper.driver.page_source
                    )
                    import re
                    media_elements = re.findall(r'<[^>]*(?:media|attach|upload|photo|image)[^>]*>', page_source, re.IGNORECASE)
                    self.logger.error("页面中发现的可能媒体相关元素:")
                    for elem in media_elements[:5]:
                        self.logger.error(f"   {elem}")
                except Exception:
                    pass
                raise Exception("❌ 未找到媒体上传按钮")

            # 点击媒体上传按钮
            self.logger.info("🖱️ 点击媒体上传按钮...")
            await driver_wrapper.execute_async(media_button.click)
            await asyncio.sleep(random.uniform(1.0, 2.0))

            # 查找文件输入元素
            file_input_selectors = X_SELECTORS['MEDIA_UPLOAD'].split(', ')
            file_input = None

            for selector in file_input_selectors:
                try:
                    wait = WebDriverWait(driver_wrapper.driver, 5)
                    input_element = await driver_wrapper.execute_async(
                        lambda: wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    )
                    file_input = input_element
                    self.logger.debug(f"找到文件输入: {selector}")
                    break
                except Exception:
                    continue

            if not file_input:
                self.logger.error(f"未找到文件输入元素: {file_input_selectors}")
                raise Exception("未找到文件输入元素")

            # 批量上传文件
            if len(valid_files) == 1:
                # 单文件上传
                file_path = str(Path(valid_files[0].file_path).absolute())
                self.logger.info(f"📤 发送文件路径: {file_path}")
                await driver_wrapper.execute_async(
                    lambda: file_input.send_keys(file_path)
                )
                self.logger.info(f"上传单个文件: {valid_files[0].filename}")

                # 简单等待上传完成
                await asyncio.sleep(1)

            else:
                # 多文件批量上传
                file_paths = []
                for media_file in valid_files:
                    file_paths.append(str(Path(media_file.file_path).absolute()))

                # 使用换行符分隔多个文件路径
                combined_paths = '\n'.join(file_paths)

                try:
                    self.logger.info(f"📤 发送批量文件路径: {len(file_paths)} 个文件")
                    await driver_wrapper.execute_async(
                        lambda: file_input.send_keys(combined_paths)
                    )
                    self.logger.info(f"批量上传 {len(valid_files)} 个文件")

                    # 简单等待上传完成
                    await asyncio.sleep(1)

                except Exception as batch_error:
                    self.logger.warning(f"批量上传失败，尝试逐个上传: {batch_error}")

                    # 如果批量上传失败，逐个上传
                    for i, media_file in enumerate(valid_files):
                        try:
                            if i > 0:
                                # 对于后续文件，需要重新点击媒体按钮
                                await driver_wrapper.execute_async(media_button.click)
                                await asyncio.sleep(random.uniform(0.5, 1.0))

                                # 重新查找文件输入
                                file_input = await driver_wrapper.execute_async(
                                    lambda: WebDriverWait(driver_wrapper.driver, 5).until(
                                        EC.presence_of_element_located((By.CSS_SELECTOR, file_input_selectors[0]))
                                    )
                                )

                            file_path = str(Path(media_file.file_path).absolute())
                            self.logger.info(f"📤 发送文件路径 {i+1}: {file_path}")
                            await driver_wrapper.execute_async(
                                lambda: file_input.send_keys(file_path)
                            )

                            self.logger.info(f"逐个上传文件 {i+1}/{len(valid_files)}: {media_file.filename}")

                            # 简单等待上传完成
                            await asyncio.sleep(1)

                            await asyncio.sleep(random.uniform(2, 3))

                        except Exception as individual_error:
                            self.logger.error(f"上传文件失败 {media_file.filename}: {individual_error}")
                            continue

            # 等待所有文件上传完成
            upload_wait_time = len(valid_files) * 3 + 5  # 每个文件3秒 + 额外5秒
            self.logger.info(f"等待文件上传完成，预计 {upload_wait_time} 秒...")
            await asyncio.sleep(upload_wait_time)

            # 验证上传结果
            await self._verify_media_upload(driver_wrapper, len(valid_files))

        except Exception as e:
            self.logger.error(f"上传媒体文件失败: {e}")
            raise

    async def _verify_media_upload(self, driver_wrapper, expected_count: int):
        """验证媒体上传结果 - 增强检测精度"""
        try:
            # 🎯 2024年最新X平台媒体选择器 - 基于实际DOM结构
            media_preview_selectors = [
                # 最新的媒体附件容器 - 2024年X平台结构
                '[data-testid="attachments"] div[data-testid*="media"]',
                '[data-testid="attachments"] img[src*="pbs.twimg.com"]',
                '[data-testid="attachments"] video[src*="video.twimg.com"]',

                # 媒体预览的直接选择器
                '[data-testid="attachments"] img:not([alt*="avatar"]):not([alt*="profile"]):not([src*="emoji"])',
                '[data-testid="attachments"] video:not([alt*="avatar"])',

                # 媒体容器内的按钮元素
                '[data-testid="attachments"] [role="button"] img[src]',
                '[data-testid="attachments"] [role="button"] video[src]',

                # 发帖区域的媒体预览
                'div[data-testid*="tweet"] img[src*="pbs.twimg.com"]',
                'div[data-testid*="tweet"] video[src*="video.twimg.com"]',

                # 通用媒体元素（有实际内容的）
                'img[src*="pbs.twimg.com"]:not([alt*="avatar"]):not([alt*="profile"])',
                'video[src*="video.twimg.com"]',

                # 备用选择器 - 基于aria-label
                'div[aria-label*="Image"] img[src]',
                'div[aria-label*="Video"] video[src]',
                'div[aria-label*="图片"] img[src]',
                'div[aria-label*="视频"] video[src]'
            ]

            uploaded_count = 0
            found_elements = []

            # 🔍 等待媒体元素加载
            await asyncio.sleep(2)

            for selector in media_preview_selectors:
                try:
                    elements = await driver_wrapper.execute_async(
                        lambda: driver_wrapper.driver.find_elements(By.CSS_SELECTOR, selector)
                    )

                    if elements:
                        # 过滤掉头像、图标等非内容媒体
                        valid_elements = []
                        for element in elements:
                            try:
                                # 检查元素属性，排除头像等
                                alt_text = await driver_wrapper.execute_async(
                                    lambda: element.get_attribute('alt') or ''
                                )
                                src = await driver_wrapper.execute_async(
                                    lambda: element.get_attribute('src') or ''
                                )

                                # 排除头像、图标等
                                if not any(keyword in alt_text.lower() for keyword in ['avatar', 'profile', 'icon', 'logo']):
                                    if not any(keyword in src.lower() for keyword in ['avatar', 'profile', 'icon', 'logo']):
                                        valid_elements.append(element)

                            except Exception:
                                # 如果无法获取属性，保守地认为是有效元素
                                valid_elements.append(element)

                        if valid_elements:
                            uploaded_count = len(valid_elements)
                            found_elements = valid_elements
                            self.logger.debug(f"使用选择器 {selector} 找到 {uploaded_count} 个有效媒体元素")
                            break

                except Exception as selector_error:
                    self.logger.debug(f"选择器 {selector} 检测失败: {selector_error}")
                    continue

            self.logger.info(f"媒体上传验证: 预期 {expected_count} 个，实际 {uploaded_count} 个")

            # 🎯 智能验证逻辑 - 考虑X平台的特殊情况
            if uploaded_count == expected_count:
                self.logger.info("✅ 媒体文件上传数量完全匹配")
                return True
            elif uploaded_count > 0:
                # 🔍 如果检测到的数量不匹配，进行二次验证
                if uploaded_count < expected_count:
                    self.logger.warning(f"⚠️ 检测数量不足，进行二次验证...")

                    # 等待更长时间，让媒体完全加载
                    await asyncio.sleep(3)

                    # 🔍 使用最简单但最可靠的选择器重新检测
                    simple_selectors = [
                        # 最直接的媒体选择器
                        '[data-testid="attachments"] img[src*="pbs.twimg.com"]',
                        '[data-testid="attachments"] video[src*="video.twimg.com"]',
                        # 备用选择器
                        '[data-testid="attachments"] img[src]:not([alt*="avatar"])',
                        '[data-testid="attachments"] video[src]',
                        # 最宽泛的选择器
                        '[data-testid="attachments"] img',
                        '[data-testid="attachments"] video'
                    ]

                    recheck_count = 0
                    best_count = 0

                    # 🧠 智能检测：尝试多种选择器，取最佳结果
                    for i, selector in enumerate(simple_selectors):
                        try:
                            elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                            if elements:
                                # 过滤掉明显的非内容媒体
                                valid_elements = []
                                for element in elements:
                                    try:
                                        src = await driver_wrapper.execute_async(
                                            lambda: element.get_attribute('src') or ''
                                        )
                                        alt = await driver_wrapper.execute_async(
                                            lambda: element.get_attribute('alt') or ''
                                        )

                                        # 🎯 更严格的过滤条件
                                        is_valid = (
                                            src and
                                            not any(keyword in src.lower() for keyword in
                                                   ['avatar', 'profile', 'icon', 'logo', 'emoji']) and
                                            not any(keyword in alt.lower() for keyword in
                                                   ['avatar', 'profile', 'icon', 'logo', 'emoji']) and
                                            (src.startswith('blob:') or 'pbs.twimg.com' in src or 'video.twimg.com' in src)
                                        )

                                        if is_valid:
                                            valid_elements.append(element)
                                    except:
                                        continue

                                current_count = len(valid_elements)
                                if current_count > best_count:
                                    best_count = current_count
                                    recheck_count = current_count

                                self.logger.debug(f"选择器 {i+1}: 找到 {current_count} 个有效媒体")

                                # 如果找到了预期数量，就停止检查
                                if current_count >= expected_count:
                                    break

                        except Exception as e:
                            self.logger.debug(f"选择器 {selector} 检查失败: {e}")
                            continue

                    if recheck_count >= expected_count:
                        self.logger.info(f"✅ 二次验证成功: 检测到 {recheck_count} 个媒体文件")
                        return True
                    else:
                        self.logger.warning(f"⚠️ 二次验证仍显示部分上传: {recheck_count}/{expected_count}")
                        # 🎯 宽松处理：如果至少有一半媒体上传成功，认为基本成功
                        if recheck_count >= expected_count // 2:
                            self.logger.info("✅ 部分媒体上传成功，继续发帖流程")
                            return True
                else:
                    self.logger.info("✅ 检测到足够的媒体文件")
                    return True

                return True
            else:
                self.logger.error("❌ 没有检测到已上传的媒体文件")
                # 尝试检查是否有上传错误提示
                await self._check_upload_errors(driver_wrapper)
                return False

        except Exception as e:
            self.logger.warning(f"媒体上传验证失败: {e}")
            return False

    async def _check_upload_errors(self, driver_wrapper):
        """检查上传错误提示"""
        try:
            # 常见的错误提示选择器
            error_selectors = [
                '[role="alert"]',
                '.error-message',
                '[data-testid="error"]',
                '.notification',
                '.toast',
                'div[style*="color: red"]',
                'div[style*="color: rgb(244, 33, 46)"]'  # X平台的红色错误提示
            ]

            for selector in error_selectors:
                try:
                    error_elements = await driver_wrapper.execute_async(
                        lambda: driver_wrapper.driver.find_elements(By.CSS_SELECTOR, selector)
                    )

                    for element in error_elements:
                        try:
                            error_text = await driver_wrapper.execute_async(
                                lambda: element.text or element.get_attribute('textContent')
                            )

                            if error_text and len(error_text.strip()) > 0:
                                self.logger.error(f"检测到上传错误: {error_text}")

                        except Exception:
                            continue

                except Exception:
                    continue

            # 检查特定的视频错误提示
            video_error_texts = [
                "Your video file could not be processed",
                "视频文件无法处理",
                "Video upload failed",
                "视频上传失败"
            ]

            page_text = await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.find_element(By.TAG_NAME, 'body').text
            )

            for error_text in video_error_texts:
                if error_text.lower() in page_text.lower():
                    self.logger.error(f"检测到视频错误: {error_text}")
                    break

        except Exception as e:
            self.logger.debug(f"错误检查失败: {e}")

    async def _find_tweet_button_with_fallback(self, driver_wrapper, selectors):
        """
        使用多重备用策略查找发帖按钮 - 参考技术文档

        Args:
            driver_wrapper: WebDriver包装器
            selectors: 选择器列表

        Returns:
            dict: 包含按钮元素和选择器的字典，未找到返回None
        """
        try:
            # 策略1: 主要选择器直接查找
            for selector in selectors[:4]:  # 前4个是主要选择器
                try:
                    elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        button = elements[0]
                        if await self._verify_tweet_button(driver_wrapper, button):
                            self.logger.info(f"✅ 策略1成功: {selector}")
                            return {'element': button, 'selector': selector}
                except Exception as e:
                    self.logger.debug(f"策略1失败 {selector}: {e}")
                    continue

            # 策略2: 备用选择器查找
            for selector in selectors[4:]:
                try:
                    wait = WebDriverWait(driver_wrapper.driver, 2)
                    button = await driver_wrapper.execute_async(
                        lambda: wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    )
                    if await self._verify_tweet_button(driver_wrapper, button):
                        self.logger.info(f"✅ 策略2成功: {selector}")
                        return {'element': button, 'selector': selector}
                except Exception as e:
                    self.logger.debug(f"策略2失败 {selector}: {e}")
                    continue

            # 策略3: 通用按钮查找 + 文本验证
            try:
                generic_selectors = [
                    'button[role="button"]',
                    '[role="button"]',
                    'button[type="button"]'
                ]

                for generic_selector in generic_selectors:
                    elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, generic_selector)
                    for button in elements:
                        if await self._verify_tweet_button_by_text(driver_wrapper, button):
                            self.logger.info(f"✅ 策略3成功: {generic_selector}")
                            return {'element': button, 'selector': generic_selector}
            except Exception as e:
                self.logger.debug(f"策略3失败: {e}")

            return None

        except Exception as e:
            self.logger.error(f"查找发帖按钮失败: {e}")
            return None

    async def _verify_tweet_button(self, driver_wrapper, button):
        """验证按钮是否为发帖按钮"""
        try:
            # 检查可见性
            is_visible = await driver_wrapper.execute_async(lambda: button.is_displayed())
            if not is_visible:
                return False

            # 检查data-testid
            test_id = await driver_wrapper.execute_async(
                lambda: button.get_attribute('data-testid') or ''
            )
            if 'tweet' in test_id.lower() and 'button' in test_id.lower():
                return True

            # 检查aria-label
            aria_label = await driver_wrapper.execute_async(
                lambda: button.get_attribute('aria-label') or ''
            )
            valid_labels = ['post', '发布', 'tweet', '推文']
            invalid_labels = ['reply', '回复', 'retweet', '转推']

            if any(label in aria_label.lower() for label in valid_labels) and \
               not any(label in aria_label.lower() for label in invalid_labels):
                return True

            return False

        except Exception:
            return False

    async def _verify_tweet_button_by_text(self, driver_wrapper, button):
        """通过文本内容验证按钮是否为发帖按钮"""
        try:
            button_text = await driver_wrapper.execute_async(
                lambda: button.text or button.get_attribute('aria-label') or ''
            )

            valid_texts = ['post', '发布', 'tweet', '推文']
            invalid_texts = ['reply', '回复', 'retweet', '转推', 'like', '喜欢', 'follow', '关注']

            text_lower = button_text.lower()

            # 必须包含有效文本且不包含无效文本
            has_valid = any(valid in text_lower for valid in valid_texts)
            has_invalid = any(invalid in text_lower for invalid in invalid_texts)

            return has_valid and not has_invalid

        except Exception:
            return False

    async def _wait_for_button_enabled(self, driver_wrapper, selector):
        """智能等待按钮可用 - 参考技术文档"""
        try:
            max_wait_time = 30  # 减少等待时间
            check_interval = 0.5
            max_loops = int(max_wait_time / check_interval)

            consecutive_no_progress = 0
            max_no_progress = 5

            for loop_count in range(max_loops):
                try:
                    # 重新获取按钮
                    elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                    if not elements:
                        consecutive_no_progress += 1
                        if consecutive_no_progress >= max_no_progress:
                            break
                        await asyncio.sleep(check_interval)
                        continue

                    button = elements[0]

                    # 快速检查
                    is_displayed = await driver_wrapper.execute_async(lambda: button.is_displayed())
                    is_enabled = await driver_wrapper.execute_async(lambda: button.is_enabled())
                    is_disabled = await driver_wrapper.execute_async(lambda: button.get_attribute('aria-disabled'))

                    if is_displayed and is_enabled and is_disabled != 'true':
                        self.logger.info(f"✅ 按钮已可用 (循环 {loop_count + 1})")
                        return True

                    consecutive_no_progress = 0
                    await asyncio.sleep(check_interval)

                except Exception as e:
                    self.logger.debug(f"检查按钮状态失败: {e}")
                    consecutive_no_progress += 1
                    if consecutive_no_progress >= max_no_progress:
                        break
                    await asyncio.sleep(check_interval)

            return False

        except Exception as e:
            self.logger.error(f"等待按钮可用失败: {e}")
            return False

    async def _smart_button_recovery(self, driver_wrapper, selector):
        """
        智能按钮状态恢复 - 解决遮罩层处理后的DOM变化问题

        Args:
            driver_wrapper: WebDriver包装器
            selector: 按钮选择器
        """
        try:
            self.logger.info("🔄 智能按钮状态恢复...")

            # 策略1: 等待页面稳定
            await asyncio.sleep(1)

            # 策略2: 重新查找按钮并检查状态
            max_recovery_attempts = 5
            for attempt in range(max_recovery_attempts):
                try:
                    # 查找按钮
                    elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                    if not elements:
                        self.logger.debug(f"恢复尝试 {attempt + 1}: 按钮不存在")
                        await asyncio.sleep(0.5)
                        continue

                    button = elements[0]

                    # 检查按钮状态
                    is_displayed = await driver_wrapper.execute_async(lambda: button.is_displayed())
                    is_enabled = await driver_wrapper.execute_async(lambda: button.is_enabled())

                    if is_displayed and is_enabled:
                        self.logger.info(f"✅ 按钮状态恢复成功 (尝试 {attempt + 1})")
                        return True

                    # 如果按钮不可见，尝试滚动到按钮位置
                    if not is_displayed:
                        self.logger.debug(f"恢复尝试 {attempt + 1}: 按钮不可见，尝试滚动")
                        await driver_wrapper.execute_async(
                            lambda: driver_wrapper.driver.execute_script(
                                "arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});",
                                button
                            )
                        )
                        await asyncio.sleep(0.5)

                        # 重新检查
                        is_displayed = await driver_wrapper.execute_async(lambda: button.is_displayed())
                        if is_displayed:
                            self.logger.info(f"✅ 滚动后按钮可见 (尝试 {attempt + 1})")
                            return True

                    await asyncio.sleep(0.5)

                except Exception as e:
                    self.logger.debug(f"恢复尝试 {attempt + 1} 失败: {e}")
                    await asyncio.sleep(0.5)
                    continue

            # 策略3: 如果仍然失败，尝试刷新页面元素
            self.logger.warning("🔄 尝试页面元素刷新...")
            try:
                await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script("""
                        // 强制重新渲染页面
                        document.body.style.display = 'none';
                        document.body.offsetHeight; // 触发重排
                        document.body.style.display = '';

                        // 确保发帖按钮可见
                        const buttons = document.querySelectorAll('[data-testid*="tweet"], [data-testid*="Button"]');
                        buttons.forEach(btn => {
                            if (btn.style) {
                                btn.style.visibility = 'visible';
                                btn.style.display = '';
                                btn.style.opacity = '1';
                            }
                        });
                    """)
                )
                await asyncio.sleep(1)

                # 最后检查
                elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    button = elements[0]
                    is_displayed = await driver_wrapper.execute_async(lambda: button.is_displayed())
                    if is_displayed:
                        self.logger.info("✅ 页面刷新后按钮恢复")
                        return True

            except Exception as e:
                self.logger.debug(f"页面刷新失败: {e}")

            self.logger.warning("⚠️ 按钮状态恢复失败")
            return False

        except Exception as e:
            self.logger.error(f"智能按钮恢复异常: {e}")
            return False

    async def _smart_click_with_recovery(self, driver_wrapper, selector):
        """
        智能点击策略 - 解决Chrome内核错误和DOM变化问题

        Args:
            driver_wrapper: WebDriver包装器
            selector: 按钮选择器

        Returns:
            bool: 点击是否成功
        """
        try:
            max_click_attempts = 3

            for click_attempt in range(max_click_attempts):
                self.logger.info(f"🖱️ 智能点击尝试 {click_attempt + 1}/{max_click_attempts}")

                try:
                    # 步骤1: 检查按钮状态（更宽松的检查）
                    button_ready = await self._ensure_button_ready(driver_wrapper, selector)
                    if not button_ready and click_attempt == 0:
                        # 第一次尝试时如果按钮未就绪，尝试恢复
                        self.logger.info(f"按钮未就绪，尝试恢复: {click_attempt + 1}")
                        await self._smart_button_recovery(driver_wrapper, selector)
                        button_ready = await self._ensure_button_ready(driver_wrapper, selector)

                    if not button_ready:
                        self.logger.warning(f"按钮仍未就绪，强制尝试点击: {click_attempt + 1}")
                        # 不跳过，强制尝试点击

                    # 步骤2: 使用多种点击方式
                    click_methods = [
                        # 方法1: 标准点击
                        lambda: self._standard_click(driver_wrapper, selector),
                        # 方法2: JavaScript点击
                        lambda: self._javascript_click(driver_wrapper, selector),
                        # 方法3: 坐标点击
                        lambda: self._coordinate_click(driver_wrapper, selector),
                        # 方法4: 强制JavaScript点击
                        lambda: self._force_javascript_click(driver_wrapper, selector),
                        # 方法5: 通用发帖按钮点击
                        lambda: self._generic_tweet_button_click(driver_wrapper)
                    ]

                    for method_idx, click_method in enumerate(click_methods):
                        try:
                            self.logger.debug(f"尝试点击方法 {method_idx + 1}")
                            success = await click_method()
                            if success:
                                self.logger.info(f"✅ 点击成功 (方法 {method_idx + 1})")
                                return True
                        except Exception as method_error:
                            self.logger.debug(f"点击方法 {method_idx + 1} 失败: {method_error}")
                            continue

                    # 如果所有方法都失败，等待后重试
                    if click_attempt < max_click_attempts - 1:
                        self.logger.warning(f"尝试 {click_attempt + 1} 失败，等待后重试...")
                        await asyncio.sleep(2)

                        # 重新处理遮罩层
                        await self.anti_detection.handle_x_platform_masks(driver_wrapper)
                        await asyncio.sleep(1)

                except Exception as attempt_error:
                    self.logger.warning(f"点击尝试 {click_attempt + 1} 异常: {attempt_error}")
                    if click_attempt < max_click_attempts - 1:
                        await asyncio.sleep(2)
                    continue

            self.logger.error("所有智能点击尝试都失败")
            return False

        except Exception as e:
            self.logger.error(f"智能点击策略异常: {e}")
            return False

    async def _ensure_button_ready(self, driver_wrapper, selector):
        """确保按钮准备就绪 - 增强版"""
        try:
            elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
            if not elements:
                self.logger.debug(f"按钮不存在: {selector}")
                return False

            button = elements[0]

            # 检查1: 基本可见性
            try:
                is_displayed = await driver_wrapper.execute_async(lambda: button.is_displayed())
                if not is_displayed:
                    self.logger.debug(f"按钮不可见: {selector}")
                    return False
            except Exception as e:
                self.logger.debug(f"检查按钮可见性失败: {e}")
                return False

            # 检查2: 基本可用性
            try:
                is_enabled = await driver_wrapper.execute_async(lambda: button.is_enabled())
                if not is_enabled:
                    self.logger.debug(f"按钮被禁用: {selector}")
                    return False
            except Exception as e:
                self.logger.debug(f"检查按钮可用性失败: {e}")
                return False

            # 检查3: aria-disabled属性
            try:
                aria_disabled = await driver_wrapper.execute_async(
                    lambda: button.get_attribute('aria-disabled')
                )
                if aria_disabled == 'true':
                    self.logger.debug(f"按钮aria-disabled=true: {selector}")
                    return False
            except Exception as e:
                self.logger.debug(f"检查aria-disabled失败: {e}")
                # 这个检查失败不影响整体判断

            # 检查4: 按钮文本内容
            try:
                button_text = await driver_wrapper.execute_async(
                    lambda: button.text or button.get_attribute('aria-label') or ''
                )
                valid_texts = ['post', '发布', 'tweet', '推文', 'publish']
                if not any(text.lower() in button_text.lower() for text in valid_texts):
                    self.logger.debug(f"按钮文本不匹配: {selector}, 文本: {button_text}")
                    # 文本不匹配不一定意味着按钮不可用，继续检查
            except Exception as e:
                self.logger.debug(f"检查按钮文本失败: {e}")

            self.logger.debug(f"按钮就绪检查通过: {selector}")
            return True

        except Exception as e:
            self.logger.debug(f"按钮就绪检查异常: {e}")
            return False

    async def _standard_click(self, driver_wrapper, selector):
        """标准点击方法"""
        try:
            elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                button = elements[0]
                await driver_wrapper.execute_async(lambda: button.click())
                return True
            return False
        except Exception:
            return False

    async def _javascript_click(self, driver_wrapper, selector):
        """JavaScript点击方法"""
        try:
            elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                button = elements[0]
                await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script("arguments[0].click();", button)
                )
                return True
            return False
        except Exception:
            return False

    async def _coordinate_click(self, driver_wrapper, selector):
        """坐标点击方法"""
        try:
            elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                button = elements[0]
                # 获取按钮位置
                location = await driver_wrapper.execute_async(lambda: button.location)
                size = await driver_wrapper.execute_async(lambda: button.size)

                # 计算中心点
                center_x = location['x'] + size['width'] // 2
                center_y = location['y'] + size['height'] // 2

                # 使用ActionChains点击坐标
                await driver_wrapper.execute_async(
                    lambda: ActionChains(driver_wrapper.driver).move_by_offset(center_x, center_y).click().perform()
                )
                return True
            return False
        except Exception:
            return False

    async def _force_javascript_click(self, driver_wrapper, selector):
        """强制JavaScript点击方法"""
        try:
            self.logger.info(f"尝试强制JavaScript点击: {selector}")

            # 使用更激进的JavaScript点击
            script = f"""
            var buttons = document.querySelectorAll('{selector}');
            for (var i = 0; i < buttons.length; i++) {{
                var button = buttons[i];
                if (button && button.offsetParent !== null) {{
                    // 强制触发点击事件
                    button.click();

                    // 触发鼠标事件
                    var event = new MouseEvent('click', {{
                        view: window,
                        bubbles: true,
                        cancelable: true
                    }});
                    button.dispatchEvent(event);

                    console.log('强制点击执行完成');
                    return true;
                }}
            }}
            return false;
            """

            result = await driver_wrapper.execute_script(script)
            if result:
                self.logger.info("强制JavaScript点击成功")
                await asyncio.sleep(2)
                return True
            else:
                self.logger.warning("强制JavaScript点击未找到有效按钮")
                return False

        except Exception as e:
            self.logger.error(f"强制JavaScript点击失败: {e}")
            return False

    async def _generic_tweet_button_click(self, driver_wrapper):
        """通用发帖按钮点击方法"""
        try:
            self.logger.info("尝试通用发帖按钮点击")

            # 尝试多种可能的发帖按钮选择器
            generic_selectors = [
                'button[data-testid="tweetButton"]',
                'button[data-testid="tweetButtonInline"]',
                'button[aria-label*="Tweet"]',
                'button[aria-label*="Post"]',
                'button[aria-label*="发布"]',
                'button[aria-label*="推文"]',
                'div[data-testid="tweetButton"]',
                'div[role="button"][data-testid="tweetButton"]'
            ]

            for selector in generic_selectors:
                try:
                    # 使用JavaScript查找并点击
                    script = f"""
                    var elements = document.querySelectorAll('{selector}');
                    for (var i = 0; i < elements.length; i++) {{
                        var element = elements[i];
                        if (element && element.offsetParent !== null) {{
                            element.click();
                            return true;
                        }}
                    }}
                    return false;
                    """

                    result = await driver_wrapper.execute_script(script)
                    if result:
                        self.logger.info(f"通用按钮点击成功: {selector}")
                        await asyncio.sleep(2)
                        return True

                except Exception as e:
                    self.logger.debug(f"通用选择器失败: {selector}, {e}")
                    continue

            self.logger.warning("所有通用发帖按钮选择器都失败")
            return False

        except Exception as e:
            self.logger.error(f"通用发帖按钮点击失败: {e}")
            return False

    async def _conservative_click_strategy(self, driver_wrapper, selector):
        """保守的点击策略 - 防止误点击和遮罩连锁反应"""
        try:
            self.logger.info(f"🔒 开始保守点击策略: {selector}")

            # 步骤1: 最后一次按钮验证
            elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
            if not elements:
                self.logger.error("❌ 按钮已消失，停止点击")
                return False

            button = elements[0]

            # 步骤2: 验证按钮位置在发帖区域内
            try:
                location = await driver_wrapper.execute_async(lambda: button.location)
                size = await driver_wrapper.execute_async(lambda: button.size)

                # 检查按钮是否在合理位置（不在页面边缘）
                if location['x'] < 50 or location['y'] < 50:
                    self.logger.warning(f"⚠️ 按钮位置异常: {location}, 可能不是发帖按钮")
                    return False

                self.logger.info(f"✅ 按钮位置验证通过: {location}")

            except Exception as e:
                self.logger.warning(f"按钮位置检查失败: {e}")
                # 位置检查失败不阻止点击，但要更加小心

            # 步骤3: 单次精确点击，不重试
            try:
                self.logger.info("🎯 执行单次精确点击...")

                # 使用最简单、最可靠的点击方法
                await driver_wrapper.execute_async(lambda: button.click())

                self.logger.info("✅ 点击执行完成")
                await asyncio.sleep(2)

                # 步骤4: 立即检查是否产生了遮罩
                mask_elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, '[data-testid="mask"], [role="dialog"], [aria-modal="true"]')
                if mask_elements:
                    self.logger.error("❌ 点击后检测到遮罩层，点击可能有误")
                    return False

                self.logger.info("✅ 点击后无遮罩层，点击成功")
                return True

            except Exception as e:
                self.logger.error(f"精确点击失败: {e}")
                return False

        except Exception as e:
            self.logger.error(f"保守点击策略异常: {e}")
            return False

    async def _ensure_in_compose_area(self, driver_wrapper):
        """确保焦点在发帖区域内，避免误点击"""
        try:
            self.logger.info("🔒 确保焦点在发帖区域内...")

            # 查找发帖文本框，确保在正确区域
            compose_selectors = [
                '[data-testid="tweetTextarea_0"]',
                '[data-testid="tweetTextarea_1"]',
                '[contenteditable="true"][data-testid*="tweet"]',
                '[role="textbox"][data-testid*="tweet"]'
            ]

            for selector in compose_selectors:
                try:
                    elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        # 轻点击文本框，确保焦点在发帖区域
                        await driver_wrapper.execute_async(
                            lambda: elements[0].click()
                        )
                        self.logger.info(f"✅ 焦点已设置到发帖区域: {selector}")
                        await asyncio.sleep(1)
                        return True
                except Exception as e:
                    self.logger.debug(f"设置焦点失败: {selector}, {e}")
                    continue

            self.logger.warning("⚠️ 未能确保焦点在发帖区域")
            return False

        except Exception as e:
            self.logger.error(f"确保发帖区域焦点失败: {e}")
            return False

    async def _preventive_mask_check(self, driver_wrapper):
        """预防性检查遮罩层"""
        try:
            self.logger.info("🔒 预防性检查遮罩层...")

            # 检查是否已有遮罩层
            mask_selectors = [
                '[data-testid="mask"]',
                '[role="dialog"]',
                '[aria-modal="true"]'
            ]

            mask_found = False
            for selector in mask_selectors:
                try:
                    elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        mask_found = True
                        self.logger.warning(f"⚠️ 发现预存在遮罩层: {selector}")
                        break
                except Exception:
                    continue

            if mask_found:
                self.logger.warning("⚠️ 检测到遮罩层，停止发帖以避免连锁反应")
                return False
            else:
                self.logger.info("✅ 无遮罩层，可以安全继续")
                return True

        except Exception as e:
            self.logger.error(f"预防性遮罩检查失败: {e}")
            return True  # 检查失败时保守地继续

    async def _publish_post(self, driver_wrapper) -> Optional[str]:
        """
        发布帖子
        """
        try:
            self.logger.info("🚀 开始发布帖子...")

            # 🔒 步骤1: 确保页面稳定，避免误点击
            await asyncio.sleep(3)

            # 🔒 步骤2: 确保在发帖区域内，避免点击外部
            await self._ensure_in_compose_area(driver_wrapper)

            # 🔒 步骤3: 预防性检查是否已有遮罩层
            await self._preventive_mask_check(driver_wrapper)

            # 🎯 2024年X平台最精确的发布按钮选择器
            tweet_button_selectors = [
                # 🥇 最优先 - 完全精确的选择器
                'button[data-testid="tweetButtonInline"]:not([aria-disabled="true"]):not([disabled])',
                'button[data-testid="tweetButton"]:not([aria-disabled="true"]):not([disabled])',

                # 🥈 次优先 - 带可见性检查的选择器
                '[data-testid="tweetButtonInline"]:not([aria-disabled="true"]):not([style*="display: none"])',
                '[data-testid="tweetButton"]:not([aria-disabled="true"]):not([style*="display: none"])',

                # 🥉 备用 - 基于aria-label的精确选择器
                'button[aria-label*="Post tweet"]:not([aria-label*="Reply"]):not([aria-disabled="true"])',
                'button[aria-label*="发布推文"]:not([aria-label*="回复"]):not([aria-disabled="true"])',
                'button[aria-label*="Tweet"]:not([aria-label*="Reply"]):not([aria-disabled="true"])',

                # 🔄 最后备用 - 基于位置和样式的选择器
                'button[type="button"][role="button"]:not([aria-disabled="true"]):has-text("Post")',
                'button[type="button"][role="button"]:not([aria-disabled="true"]):has-text("发布")'
            ]

            tweet_button = None
            selected_selector = None

            # 🎯 智能按钮查找 - 增加容错性和重试机制
            for attempt in range(3):  # 最多尝试3次
                self.logger.info(f"🔍 按钮查找尝试 {attempt + 1}/3")

                for selector in tweet_button_selectors:
                    try:
                        # 🔄 每次尝试前都清除遮罩层 - 参考技术文档的策略
                        if attempt > 0:
                            self.logger.info(f"🛡️ 尝试 {attempt + 1}: 清除遮罩层...")
                            await self.anti_detection.handle_x_platform_masks(driver_wrapper)
                            await asyncio.sleep(1)

                            # 额外的页面稳定性检查
                            try:
                                await driver_wrapper.execute_async(
                                    lambda: driver_wrapper.driver.execute_script("window.scrollTo(0, 0);")
                                )
                                await asyncio.sleep(0.5)
                            except Exception as e:
                                self.logger.debug(f"页面滚动失败: {e}")

                        # 使用更宽松的等待策略
                        wait = WebDriverWait(driver_wrapper.driver, 3)
                        button = await driver_wrapper.execute_async(
                            lambda: wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                        )

                        # 检查按钮是否可见
                        is_visible = await driver_wrapper.execute_async(
                            lambda: button.is_displayed()
                        )

                        if not is_visible:
                            self.logger.debug(f"按钮不可见，跳过: {selector}")
                            continue

                        # 🔍 验证按钮是否真的是发布按钮
                        button_text = await driver_wrapper.execute_async(
                            lambda: button.text or button.get_attribute('aria-label') or ''
                        )

                        # 获取按钮的data-testid属性
                        test_id = await driver_wrapper.execute_async(
                            lambda: button.get_attribute('data-testid') or ''
                        )

                        # 🎯 多重验证确保是正确的发布按钮
                        valid_texts = ['Post', '发布', 'Tweet', '推文', 'Publish']
                        invalid_texts = ['Reply', '回复', 'Retweet', '转推', 'Like', '喜欢', 'Share', '分享', 'Follow', '关注']
                        valid_test_ids = ['tweetButtonInline', 'tweetButton']

                        is_valid_button = (
                            # 检查data-testid
                            any(valid_id in test_id for valid_id in valid_test_ids) or
                            # 检查按钮文本
                            (any(valid_text.lower() in button_text.lower() for valid_text in valid_texts) and
                             not any(invalid_text.lower() in button_text.lower() for invalid_text in invalid_texts))
                        )

                        if is_valid_button:
                            tweet_button = button
                            selected_selector = selector
                            self.logger.info(f"✅ 找到有效发布按钮: {selector} (尝试 {attempt + 1})")
                            break
                        else:
                            self.logger.debug(f"按钮验证失败: {selector} (文本: {button_text}, testid: {test_id})")

                    except Exception as e:
                        self.logger.debug(f"选择器 {selector} 失败: {e}")
                        continue

                if tweet_button:
                    self.logger.info(f"🎯 成功找到发布按钮，跳出重试循环")
                    break
                else:
                    self.logger.warning(f"⚠️ 尝试 {attempt + 1} 未找到按钮，等待后重试...")
                    await asyncio.sleep(2)

            if not tweet_button:
                # 🚨 最后的备用方案：使用最简单的选择器
                self.logger.warning("🚨 使用备用方案查找发布按钮...")
                simple_selectors = [
                    '[data-testid="tweetButtonInline"]',
                    '[data-testid="tweetButton"]',
                    'button[role="button"]:has-text("Post")',
                    'button[role="button"]:has-text("发布")'
                ]

                for simple_selector in simple_selectors:
                    try:
                        elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, simple_selector)
                        if elements:
                            for element in elements:
                                # 简单检查：不是禁用状态
                                is_disabled = await driver_wrapper.execute_async(
                                    lambda: element.get_attribute('aria-disabled')
                                )
                                if is_disabled != 'true':
                                    tweet_button = element
                                    selected_selector = simple_selector
                                    self.logger.info(f"🆘 备用方案找到按钮: {simple_selector}")
                                    break
                        if tweet_button:
                            break
                    except Exception as e:
                        self.logger.debug(f"备用选择器 {simple_selector} 失败: {e}")
                        continue

                if not tweet_button:
                    raise Exception("所有方案都无法找到发布按钮")
            
            # 等待按钮可用（不是disabled状态）
            max_wait_time = 45  # 最多等待45秒
            wait_interval = 0.5   # 每0.5秒检查一次

            button_enabled = False

            for attempt in range(int(max_wait_time / wait_interval)):
                try:
                    # 重新获取按钮元素（防止DOM变化）
                    elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selected_selector)
                    if not elements:
                        self.logger.debug(f"发布按钮不存在，等待中... (尝试 {attempt + 1})")
                        await asyncio.sleep(wait_interval)
                        continue

                    button = elements[0]

                    # 检查按钮是否可见
                    is_displayed = await driver_wrapper.execute_async(lambda: button.is_displayed())
                    if not is_displayed:
                        self.logger.debug(f"发布按钮不可见，等待中... (尝试 {attempt + 1})")
                        await asyncio.sleep(wait_interval)
                        continue

                    # 检查按钮是否可用
                    is_enabled = await driver_wrapper.execute_async(lambda: button.is_enabled())
                    is_disabled = await driver_wrapper.execute_async(lambda: button.get_attribute('aria-disabled'))
                    disabled_attr = await driver_wrapper.execute_async(lambda: button.get_attribute('disabled'))

                    # 检查按钮文本内容（确保是正确的发布按钮）
                    button_text = await driver_wrapper.execute_async(lambda: button.text)
                    valid_texts = ['Post', '发布', 'Tweet', '推文']

                    if (is_enabled and
                        is_disabled != 'true' and
                        disabled_attr is None and
                        any(text in button_text for text in valid_texts if button_text)):

                        # 额外检查：确保按钮可以点击
                        try:
                            await driver_wrapper.execute_async(
                                lambda: ActionChains(driver_wrapper.driver).move_to_element(button).perform()
                            )
                            button_enabled = True
                            self.logger.debug(f"发布按钮已可用 (尝试 {attempt + 1}, 文本: {button_text})")
                            break
                        except Exception:
                            self.logger.debug(f"发布按钮无法悬停，继续等待... (尝试 {attempt + 1})")
                    else:
                        self.logger.debug(f"发布按钮仍被禁用，等待中... (尝试 {attempt + 1}, enabled: {is_enabled}, aria-disabled: {is_disabled}, disabled: {disabled_attr}, 文本: {button_text})")

                    await asyncio.sleep(wait_interval)

                except Exception as check_error:
                    self.logger.warning(f"检查按钮状态失败: {check_error}")
                    await asyncio.sleep(wait_interval)
            
            if not button_enabled:
                # 尝试其他选择器
                self.logger.warning(f"主选择器 {selected_selector} 的按钮未能启用，尝试其他选择器...")

                for backup_selector in tweet_button_selectors[1:]:
                    try:
                        elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, backup_selector)
                        if elements:
                            backup_button = elements[0]
                            is_displayed = await driver_wrapper.execute_async(lambda: backup_button.is_displayed())
                            is_enabled = await driver_wrapper.execute_async(lambda: backup_button.is_enabled())
                            is_disabled = await driver_wrapper.execute_async(lambda: backup_button.get_attribute('aria-disabled'))
                            disabled_attr = await driver_wrapper.execute_async(lambda: backup_button.get_attribute('disabled'))

                            if is_displayed and is_enabled and is_disabled != 'true' and disabled_attr is None:
                                tweet_button = backup_button
                                selected_selector = backup_selector
                                button_enabled = True
                                self.logger.info(f"使用备用选择器找到可用按钮: {backup_selector}")
                                break
                    except Exception:
                        continue

                if not button_enabled:
                    raise Exception(f"发布按钮在等待时间内未变为可用状态，已尝试所有选择器")

            # 简单等待确保页面稳定
            await asyncio.sleep(0.5)

            # 🔒 禁用激进遮罩清除，避免连锁反应
            self.logger.info("🔒 跳过激进遮罩清除，使用保守策略...")

            # 只做轻量级检查，不进行清除操作
            # await self.anti_detection.handle_x_platform_masks(driver_wrapper)
            await asyncio.sleep(1)

            # 策略2: 额外的JavaScript清除
            try:
                await driver_wrapper.execute_async(lambda: driver_wrapper.driver.execute_script("""
                    // 移除可能的遮罩层
                    const masks = document.querySelectorAll('[role="dialog"], .modal, [data-testid*="modal"], [class*="overlay"], [class*="mask"]');
                    masks.forEach(mask => {
                        if (mask && mask.style) {
                            mask.style.display = 'none';
                            mask.style.visibility = 'hidden';
                        }
                    });

                    // 确保发帖按钮可见
                    const tweetButtons = document.querySelectorAll('[data-testid="tweetButtonInline"], [data-testid="tweetButton"]');
                    tweetButtons.forEach(btn => {
                        if (btn && btn.style) {
                            btn.style.zIndex = '9999';
                            btn.style.position = 'relative';
                        }
                    });
                """))
                self.logger.debug("✅ JavaScript遮罩层清除完成")
            except Exception as e:
                self.logger.debug(f"JavaScript遮罩层清除失败: {e}")

            # 策略3: 按ESC键清除可能的弹窗
            try:
                await driver_wrapper.execute_async(
                    lambda: ActionChains(driver_wrapper.driver).send_keys(Keys.ESCAPE).perform()
                )
                await asyncio.sleep(0.3)
                self.logger.debug("✅ ESC键清除完成")
            except Exception as e:
                self.logger.debug(f"ESC键清除失败: {e}")

            # 模拟人类行为
            await self.anti_detection.simulate_human_behavior(driver_wrapper)

            # 🎯 智能按钮状态恢复 - 解决DOM变化问题
            await self._smart_button_recovery(driver_wrapper, selected_selector)

            # 🔒 最后一次遮罩检查，确保安全
            if not await self._preventive_mask_check(driver_wrapper):
                raise Exception("检测到遮罩层，停止点击以避免连锁反应")

            # 🎯 保守的智能点击策略 - 防止误点击
            click_success = await self._conservative_click_strategy(driver_wrapper, selected_selector)

            if not click_success:
                raise Exception("保守点击策略失败，避免进一步操作")

            # 等待发布完成
            await asyncio.sleep(random.uniform(3, 5))

            # 获取发布后的URL
            current_url = driver_wrapper.current_url

            # 🔍 智能检查发布结果和URL获取
            if '/status/' in current_url:
                self.logger.info(f"✅ 直接获取到帖子URL: {current_url}")
                return current_url
            else:
                self.logger.info("🔍 未直接获取到URL，尝试从主页查找最新帖子...")

                # 🎯 多种方法获取最新帖子URL
                post_url = await self._get_latest_post_url(driver_wrapper)
                if post_url:
                    self.logger.info(f"✅ 从主页获取到帖子URL: {post_url}")
                    return post_url
                else:
                    self.logger.warning("⚠️ 无法获取帖子URL，但发帖可能已成功")
                    return None
                
        except Exception as e:
            self.logger.error(f"发布帖子失败: {e}")
            raise

    async def _get_latest_post_url(self, driver_wrapper) -> Optional[str]:
        """🔍 获取最新发布的帖子URL"""
        try:
            # 导航到主页
            await driver_wrapper.get(X_URLS['HOME'])

            # 等待页面加载完成
            await driver_wrapper.execute_async(
                lambda: WebDriverWait(driver_wrapper.driver, 15).until(
                    lambda d: d.execute_script('return document.readyState') == 'complete'
                )
            )

            # 额外等待时间让内容加载
            await asyncio.sleep(random.uniform(3, 5))

            # 🎯 多种选择器尝试找到最新帖子
            tweet_selectors = [
                'article[data-testid="tweet"]',
                '[data-testid="tweet"]',
                'article[role="article"]',
                'div[data-testid="tweet"]'
            ]

            for tweet_selector in tweet_selectors:
                try:
                    # 查找所有推文
                    tweets = await driver_wrapper.find_elements(By.CSS_SELECTOR, tweet_selector)

                    if tweets:
                        # 检查前几个推文，找到最新的
                        for i, tweet in enumerate(tweets[:3]):  # 只检查前3个
                            try:
                                # 查找推文中的链接
                                link_selectors = [
                                    'a[href*="/status/"]',
                                    'a[href*="/status/"][role="link"]',
                                    'time[datetime] ~ a[href*="/status/"]',
                                    'time[datetime] + a[href*="/status/"]'
                                ]

                                for link_selector in link_selectors:
                                    try:
                                        link_element = await driver_wrapper.execute_async(
                                            lambda: tweet.find_element(By.CSS_SELECTOR, link_selector)
                                        )

                                        if link_element:
                                            href = await driver_wrapper.execute_async(
                                                lambda: link_element.get_attribute('href')
                                            )

                                            if href and '/status/' in href:
                                                # 确保URL格式正确
                                                if href.startswith('/'):
                                                    href = f"https://x.com{href}"
                                                elif not href.startswith('http'):
                                                    href = f"https://x.com/{href}"

                                                self.logger.info(f"找到帖子链接 (推文 {i+1}): {href}")
                                                return href
                                    except Exception:
                                        continue
                            except Exception:
                                continue

                        # 如果找到推文但没有链接，记录调试信息
                        self.logger.debug(f"找到 {len(tweets)} 个推文，但未找到有效链接")
                        break

                except Exception as e:
                    self.logger.debug(f"选择器 {tweet_selector} 失败: {e}")
                    continue

            # 🔄 备用方法：通过用户资料页面获取
            try:
                self.logger.info("尝试备用方法：从用户资料页面获取最新帖子")

                # 获取当前用户名
                username_element = await driver_wrapper.find_element(
                    By.CSS_SELECTOR, '[data-testid="UserName"]'
                )
                if username_element:
                    username_text = await driver_wrapper.execute_async(
                        lambda: username_element.text
                    )
                    if username_text and '@' in username_text:
                        username = username_text.split('@')[1].split()[0]
                        profile_url = f"https://x.com/{username}"

                        await driver_wrapper.get(profile_url)
                        await asyncio.sleep(3)

                        # 在资料页面查找最新推文
                        first_tweet = await driver_wrapper.find_element(
                            By.CSS_SELECTOR, 'article[data-testid="tweet"]'
                        )
                        if first_tweet:
                            tweet_link = await driver_wrapper.execute_async(
                                lambda: first_tweet.find_element(By.CSS_SELECTOR, 'a[href*="/status/"]')
                            )
                            if tweet_link:
                                href = await driver_wrapper.execute_async(
                                    lambda: tweet_link.get_attribute('href')
                                )
                                if href:
                                    return href if href.startswith('http') else f"https://x.com{href}"
            except Exception as e:
                self.logger.debug(f"备用方法失败: {e}")

            return None

        except Exception as e:
            self.logger.error(f"获取最新帖子URL失败: {e}")
            return None




    

    
    def _update_account_post_count(self, account: Account):
        """更新账号发帖数"""
        session = get_db_session()
        try:
            db_account = session.query(Account).filter(
                Account.id == account.id
            ).first()

            if db_account:
                db_account.post_count += 1
                db_account.last_active_time = datetime.now()
                db_account.updated_at = datetime.now()
                session.commit()

        except Exception as e:
            session.rollback()
            self.logger.error(f"更新账号发帖数失败: {e}")
        finally:
            session.close()

    async def _navigate_to_compose_page(self, driver_wrapper: SeleniumDriverWrapper) -> bool:
        """智能导航到发帖页面"""
        try:
            self.logger.info("开始导航到发帖页面...")

            # 方法1: 尝试点击发帖按钮
            post_button_selectors = [
                '[data-testid="SideNav_NewTweet_Button"]',  # 侧边栏发帖按钮
                '[aria-label*="发推"]',  # 中文发推按钮
                '[aria-label*="Tweet"]',  # 英文Tweet按钮
                '[aria-label*="Post"]',  # 英文Post按钮
                'a[href="/compose/tweet"]',  # 发帖链接
                'button[aria-label*="写推文"]',  # 其他中文变体
                'button[aria-label*="Compose"]'  # 其他英文变体
            ]

            for selector in post_button_selectors:
                try:
                    element = await driver_wrapper.execute_async(
                        lambda: WebDriverWait(driver_wrapper.driver, 3).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    )

                    if element:
                        self.logger.info(f"找到发帖按钮: {selector}")
                        await self.anti_detection.simulate_human_behavior(driver_wrapper)

                        # 点击按钮
                        await driver_wrapper.execute_async(
                            lambda: driver_wrapper.driver.execute_script("arguments[0].click();", element)
                        )

                        # 等待发帖页面加载
                        await asyncio.sleep(2)

                        # 检查是否成功进入发帖页面
                        if await self._check_compose_page_loaded(driver_wrapper):
                            self.logger.info("成功通过按钮导航到发帖页面")
                            return True

                except Exception as e:
                    self.logger.debug(f"按钮 {selector} 不可用: {e}")
                    continue

            # 方法2: 尝试直接URL导航
            self.logger.info("尝试直接URL导航...")
            await driver_wrapper.get(X_URLS['COMPOSE'])
            await asyncio.sleep(3)

            if await self._check_compose_page_loaded(driver_wrapper):
                self.logger.info("成功通过URL导航到发帖页面")
                return True

            # 方法3: 尝试键盘快捷键 (N)
            self.logger.info("尝试键盘快捷键...")
            try:
                from selenium.webdriver.common.keys import Keys
                from selenium.webdriver.common.action_chains import ActionChains

                actions = ActionChains(driver_wrapper.driver)
                actions.send_keys('n').perform()  # X平台的发帖快捷键
                await asyncio.sleep(2)

                if await self._check_compose_page_loaded(driver_wrapper):
                    self.logger.info("成功通过快捷键导航到发帖页面")
                    return True

            except Exception as e:
                self.logger.debug(f"快捷键方法失败: {e}")

            self.logger.error("所有导航方法都失败了")
            return False

        except Exception as e:
            self.logger.error(f"导航到发帖页面失败: {e}")
            return False

    async def _check_compose_page_loaded(self, driver_wrapper: SeleniumDriverWrapper) -> bool:
        """检查发帖页面是否已加载"""
        try:
            # 检查发帖文本框是否存在
            compose_selectors = [
                '[data-testid="tweetTextarea_0"]',
                '[role="textbox"][aria-label*="推文"]',
                '[role="textbox"][aria-label*="Tweet"]',
                '[role="textbox"][aria-label*="Post"]',
                '[contenteditable="true"][aria-label*="推文"]',
                '[contenteditable="true"][aria-label*="Tweet"]',
                '[contenteditable="true"][aria-label*="Post"]'
            ]

            for selector in compose_selectors:
                try:
                    element = await driver_wrapper.execute_async(
                        lambda: WebDriverWait(driver_wrapper.driver, 2).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                    )
                    if element:
                        self.logger.info(f"发帖页面已加载，找到文本框: {selector}")
                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.debug(f"检查发帖页面失败: {e}")
            return False
