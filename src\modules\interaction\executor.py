#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
互动执行器模块 - Selenium版本
"""

import asyncio
import random
from datetime import datetime
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException

from src.database.connection import get_db_session
from src.database.models import Account, InteractionTask
from src.core.browser_manager import get_browser_pool
from src.core.anti_detection import AntiDetectionEngine
from src.modules.posting.executor import LoginManager
from src.utils.logger import LoggerMixin
from src.config.constants import X_SELECTORS, INTERACTION_TYPES, TASK_STATUS
from src.config.settings import get_settings


class CommentGenerator(LoggerMixin):
    """评论生成器"""
    
    def __init__(self):
        self.comment_templates = [
            "很棒的分享！👍",
            "说得太对了！",
            "学到了，谢谢分享",
            "有道理！",
            "支持！",
            "赞同这个观点",
            "很有启发性",
            "感谢分享这个信息",
            "非常有用的内容",
            "完全同意！",
            "这个想法很不错",
            "值得思考",
            "很好的观点",
            "受益匪浅",
            "说到心坎里了"
        ]
        
        self.emoji_list = [
            "👍", "👏", "💯", "🔥", "❤️", "😊", "🙌", "✨", 
            "💪", "👌", "🎯", "💡", "🚀", "⭐", "🌟"
        ]
    
    def generate_comment(self, post_content: str = None) -> str:
        """
        生成评论内容
        
        Args:
            post_content: 帖子内容（用于生成相关评论）
            
        Returns:
            评论内容
        """
        try:
            # 基础评论模板
            comment = random.choice(self.comment_templates)
            
            # 随机添加表情
            if random.random() < 0.7:  # 70%概率添加表情
                emoji = random.choice(self.emoji_list)
                if emoji not in comment:
                    comment += f" {emoji}"
            
            return comment
            
        except Exception as e:
            self.logger.warning(f"生成评论失败: {e}")
            return "很棒的分享！👍"


class InteractionExecutor(LoggerMixin):
    """互动执行器"""
    
    def __init__(self):
        self.settings = get_settings()
        self.browser_pool = get_browser_pool()
        self.anti_detection = AntiDetectionEngine()
        self.login_manager = LoginManager()
        self.comment_generator = CommentGenerator()
    
    async def execute_interaction(self, account: Account, post_url: str,
                                action_type: str, comment_content: str = None) -> bool:
        """
        执行互动操作

        Args:
            account: 账号对象
            post_url: 帖子URL
            action_type: 互动类型
            comment_content: 评论内容（评论时使用）

        Returns:
            是否成功
        """
        driver_wrapper = None
        try:
            self.logger.info(f"开始互动: {account.username}, {action_type}, {post_url}")

            # 获取WebDriver
            driver_wrapper = await self.browser_pool.get_driver(account)
            if not driver_wrapper:
                raise Exception("获取WebDriver失败")

            # 确保账号已登录
            if not await self.login_manager.login_account(account):
                raise Exception("账号登录失败")

            # 访问帖子
            await driver_wrapper.get(post_url)
            # 等待页面加载完成
            await driver_wrapper.execute_async(
                lambda: WebDriverWait(driver_wrapper.driver, 10).until(
                    lambda d: d.execute_script('return document.readyState') == 'complete'
                )
            )
            await self.anti_detection.simulate_human_behavior(driver_wrapper)

            # 执行具体互动操作
            success = False
            if action_type == INTERACTION_TYPES['LIKE']:
                success = await self._execute_like(driver_wrapper)
            elif action_type == INTERACTION_TYPES['RETWEET']:
                success = await self._execute_retweet(driver_wrapper)
            elif action_type == INTERACTION_TYPES['COMMENT']:
                if not comment_content:
                    comment_content = self.comment_generator.generate_comment()
                success = await self._execute_comment(driver_wrapper, comment_content)
            else:
                raise Exception(f"不支持的互动类型: {action_type}")

            if success:
                self.logger.info(f"互动成功: {account.username}, {action_type}")
            else:
                self.logger.warning(f"互动失败: {account.username}, {action_type}")

            return success

        except Exception as e:
            self.logger.error(f"互动异常: {account.username}, {action_type}, {e}")
            return False
    
    async def _execute_like(self, driver_wrapper) -> bool:
        """执行点赞"""
        try:
            # 查找点赞按钮
            wait = WebDriverWait(driver_wrapper.driver, 10)
            like_button = await driver_wrapper.execute_async(
                lambda: wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, X_SELECTORS['LIKE_BUTTON'])))
            )

            # 检查是否已经点赞
            button_aria_label = await driver_wrapper.execute_async(
                lambda: like_button.get_attribute('aria-label')
            )
            if button_aria_label and 'liked' in button_aria_label.lower():
                self.logger.debug("已经点赞过了")
                return True

            # 模拟人类行为
            await self.anti_detection.simulate_human_behavior(driver_wrapper)

            # 点击点赞
            await self.anti_detection.safe_click(driver_wrapper, X_SELECTORS['LIKE_BUTTON'])

            # 等待操作完成
            await asyncio.sleep(random.uniform(1, 2))
            
            return True
            
        except Exception as e:
            self.logger.error(f"执行点赞失败: {e}")
            return False
    
    async def _execute_retweet(self, driver_wrapper) -> bool:
        """执行转发"""
        try:
            # 查找转发按钮
            wait = WebDriverWait(driver_wrapper.driver, 10)
            retweet_button = await driver_wrapper.execute_async(
                lambda: wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, X_SELECTORS['RETWEET_BUTTON'])))
            )

            # 模拟人类行为
            await self.anti_detection.simulate_human_behavior(driver_wrapper)

            # 点击转发按钮
            await self.anti_detection.safe_click(driver_wrapper, X_SELECTORS['RETWEET_BUTTON'])

            # 等待转发菜单出现
            await asyncio.sleep(random.uniform(0.5, 1.0))

            # 点击确认转发
            wait = WebDriverWait(driver_wrapper.driver, 5)
            confirm_button = await driver_wrapper.execute_async(
                lambda: wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, X_SELECTORS['RETWEET_CONFIRM'])))
            )
            await driver_wrapper.execute_async(confirm_button.click)

            # 等待操作完成
            await asyncio.sleep(random.uniform(1, 2))

            return True

        except Exception as e:
            self.logger.error(f"执行转发失败: {e}")
            return False
    
    async def _execute_comment(self, driver_wrapper, comment_content: str) -> bool:
        """执行评论"""
        try:
            # 查找回复按钮
            wait = WebDriverWait(driver_wrapper.driver, 10)
            reply_button = await driver_wrapper.execute_async(
                lambda: wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, X_SELECTORS['REPLY_BUTTON'])))
            )

            # 模拟人类行为
            await self.anti_detection.simulate_human_behavior(driver_wrapper)

            # 点击回复按钮
            await self.anti_detection.safe_click(driver_wrapper, X_SELECTORS['REPLY_BUTTON'])

            # 等待回复框出现
            await asyncio.sleep(random.uniform(1, 2))

            # 输入评论内容
            wait = WebDriverWait(driver_wrapper.driver, 5)
            comment_textarea = await driver_wrapper.execute_async(
                lambda: wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, X_SELECTORS['TWEET_COMPOSE'])))
            )

            # 模拟人类打字
            await self.anti_detection.safe_type(driver_wrapper, X_SELECTORS['TWEET_COMPOSE'], comment_content)

            # 等待输入完成
            await asyncio.sleep(random.uniform(1, 2))

            # 点击发送按钮
            wait = WebDriverWait(driver_wrapper.driver, 5)
            send_button = await driver_wrapper.execute_async(
                lambda: wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, '[data-testid="tweetButton"]')))
            )
            await driver_wrapper.execute_async(send_button.click)

            # 等待发送完成
            await asyncio.sleep(random.uniform(2, 3))

            return True

        except Exception as e:
            self.logger.error(f"执行评论失败: {e}")
            return False
    
    async def batch_interact(self, accounts: List[Account], post_url: str,
                           interaction_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        批量互动
        
        Args:
            accounts: 账号列表
            post_url: 帖子URL
            interaction_config: 互动配置
            
        Returns:
            执行结果统计
        """
        try:
            self.logger.info(f"开始批量互动: {post_url}")
            
            # 随机选择账号进行不同类型的互动
            like_count = interaction_config.get('like_count', 0)
            retweet_count = interaction_config.get('retweet_count', 0)
            comment_count = interaction_config.get('comment_count', 0)
            
            # 随机分配账号
            random.shuffle(accounts)
            
            like_accounts = accounts[:like_count]
            retweet_accounts = accounts[like_count:like_count + retweet_count]
            comment_accounts = accounts[like_count + retweet_count:like_count + retweet_count + comment_count]
            
            # 创建互动任务
            tasks = []
            
            # 点赞任务
            for account in like_accounts:
                task = self._create_interaction_task(
                    account, post_url, INTERACTION_TYPES['LIKE']
                )
                if task:
                    tasks.append((account, INTERACTION_TYPES['LIKE'], None))
            
            # 转发任务
            for account in retweet_accounts:
                task = self._create_interaction_task(
                    account, post_url, INTERACTION_TYPES['RETWEET']
                )
                if task:
                    tasks.append((account, INTERACTION_TYPES['RETWEET'], None))
            
            # 评论任务
            for account in comment_accounts:
                comment_content = self.comment_generator.generate_comment()
                task = self._create_interaction_task(
                    account, post_url, INTERACTION_TYPES['COMMENT'], comment_content
                )
                if task:
                    tasks.append((account, INTERACTION_TYPES['COMMENT'], comment_content))
            
            # 🚀 执行互动任务 - 支持并发
            execution_mode = interaction_config.get('execution_mode', 'concurrent')  # 默认并发
            max_concurrent = interaction_config.get('max_concurrent', 5)  # 默认最大5个并发

            if execution_mode == 'concurrent' and len(tasks) > 1:
                self.logger.info(f"🚀 并发执行 {len(tasks)} 个互动任务 (最大并发: {max_concurrent})")
                results = await self._execute_concurrent_interactions(tasks, post_url, max_concurrent)
            else:
                self.logger.info(f"📝 串行执行 {len(tasks)} 个互动任务")
                results = await self._execute_sequential_interactions(tasks, post_url)
            
            # 统计结果
            success_count = sum(1 for r in results if r['success'])
            failed_count = len(results) - success_count
            
            result_summary = {
                'success_count': success_count,
                'failed_count': failed_count,
                'total_count': len(results),
                'details': results
            }
            
            self.logger.info(f"批量互动完成: 成功{success_count}, 失败{failed_count}")
            return result_summary

        except Exception as e:
            self.logger.error(f"批量互动失败: {e}")
            return {
                'success_count': 0,
                'failed_count': 0,
                'total_count': 0,
                'error': str(e)
            }

    async def _execute_concurrent_interactions(self, tasks: List[tuple], post_url: str, max_concurrent: int) -> List[Dict[str, Any]]:
        """
        并发执行互动任务

        Args:
            tasks: 任务列表 [(account, action_type, comment_content), ...]
            post_url: 帖子URL
            max_concurrent: 最大并发数

        Returns:
            执行结果列表
        """
        try:
            # 创建信号量控制并发数
            semaphore = asyncio.Semaphore(max_concurrent)

            async def execute_single_interaction_with_semaphore(task_info):
                """带信号量控制的单个互动执行"""
                account, action_type, comment_content = task_info

                async with semaphore:
                    try:
                        # 添加随机延迟避免同时启动
                        initial_delay = random.uniform(0.5, 2.0)
                        await asyncio.sleep(initial_delay)

                        self.logger.info(f"🎯 开始互动: {account.username} - {action_type}")

                        success = await self.execute_interaction(
                            account, post_url, action_type, comment_content
                        )

                        result = {
                            'account_id': account.id,
                            'account_username': account.username,
                            'action_type': action_type,
                            'success': success
                        }

                        if success:
                            self.logger.info(f"✅ 互动成功: {account.username} - {action_type}")
                            # 成功后短暂延迟
                            await asyncio.sleep(random.uniform(1, 3))
                        else:
                            self.logger.warning(f"❌ 互动失败: {account.username} - {action_type}")

                        return result

                    except Exception as e:
                        self.logger.error(f"互动任务执行失败: {account.username} - {action_type}, {e}")
                        return {
                            'account_id': account.id,
                            'account_username': account.username,
                            'action_type': action_type,
                            'success': False,
                            'error': str(e)
                        }

            # 创建并发任务
            concurrent_tasks = [
                execute_single_interaction_with_semaphore(task)
                for task in tasks
            ]

            # 执行所有并发任务
            self.logger.info(f"🚀 启动 {len(concurrent_tasks)} 个并发互动任务...")
            results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)

            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    account = tasks[i][0]
                    action_type = tasks[i][1]
                    processed_results.append({
                        'account_id': account.id,
                        'account_username': account.username,
                        'action_type': action_type,
                        'success': False,
                        'error': str(result)
                    })
                    self.logger.error(f"并发任务异常: {account.username} - {action_type}, {result}")
                else:
                    processed_results.append(result)

            return processed_results

        except Exception as e:
            self.logger.error(f"并发互动执行失败: {e}")
            # 返回失败结果
            return [
                {
                    'account_id': task[0].id,
                    'account_username': task[0].username,
                    'action_type': task[1],
                    'success': False,
                    'error': str(e)
                }
                for task in tasks
            ]

    async def _execute_sequential_interactions(self, tasks: List[tuple], post_url: str) -> List[Dict[str, Any]]:
        """
        串行执行互动任务

        Args:
            tasks: 任务列表 [(account, action_type, comment_content), ...]
            post_url: 帖子URL

        Returns:
            执行结果列表
        """
        results = []

        for i, (account, action_type, comment_content) in enumerate(tasks):
            try:
                self.logger.info(f"📝 串行互动 ({i+1}/{len(tasks)}): {account.username} - {action_type}")

                success = await self.execute_interaction(
                    account, post_url, action_type, comment_content
                )

                result = {
                    'account_id': account.id,
                    'account_username': account.username,
                    'action_type': action_type,
                    'success': success
                }

                results.append(result)

                if success:
                    self.logger.info(f"✅ 互动成功: {account.username} - {action_type}")
                    # 任务间延迟
                    delay = random.uniform(2, 5)
                    await asyncio.sleep(delay)
                else:
                    self.logger.warning(f"❌ 互动失败: {account.username} - {action_type}")

            except Exception as e:
                self.logger.error(f"串行互动任务执行失败: {account.username} - {action_type}, {e}")
                results.append({
                    'account_id': account.id,
                    'account_username': account.username,
                    'action_type': action_type,
                    'success': False,
                    'error': str(e)
                })

        return results
    
    def _create_interaction_task(self, account: Account, post_url: str,
                               action_type: str, comment_content: str = None) -> Optional[InteractionTask]:
        """
        创建互动任务记录
        
        Args:
            account: 账号对象
            post_url: 帖子URL
            action_type: 互动类型
            comment_content: 评论内容
            
        Returns:
            互动任务对象
        """
        session = get_db_session()
        try:
            task = InteractionTask(
                post_url=post_url,
                account_id=account.id,
                action_type=action_type,
                comment_content=comment_content,
                status=TASK_STATUS['PENDING']
            )
            
            session.add(task)
            session.commit()
            
            return task
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"创建互动任务失败: {e}")
            return None
        finally:
            session.close()
