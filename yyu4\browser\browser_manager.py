#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器管理器

负责浏览器实例的创建、配置和管理
"""

import os
import json
import time
import random
import tempfile
from typing import Dict, List, Optional, Tuple
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import (
    WebDriverException, TimeoutException, NoSuchElementException
)
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager

class BrowserManager:
    """浏览器管理器"""
    
    def __init__(self, db_manager, bot_logger):
        self.db_manager = db_manager
        self.bot_logger = bot_logger
        self.active_browsers = {}  # account_id -> browser_instance
        self.browser_configs = {}  # account_id -> config
        self.profile_paths = {}  # account_id -> profile_path

        # 创建配置文件目录
        self.profiles_dir = os.path.join(os.getcwd(), 'browser_profiles')
        if not os.path.exists(self.profiles_dir):
            os.makedirs(self.profiles_dir)
        
        # 默认配置
        self.default_config = {
            'browser_type': 'chrome',  # 'chrome' or 'firefox'
            'headless': False,
            'window_size': (1366, 768),
            'user_agent': None,
            'proxy': None,
            'timezone': None,
            'language': 'en-US',
            'webgl_vendor': None,
            'webgl_renderer': None,
            'canvas_fingerprint': None,
            'audio_fingerprint': None,
            'screen_resolution': (1920, 1080),
            'color_depth': 24,
            'pixel_ratio': 1.0,
            'hardware_concurrency': 4,
            'memory': 8,
            'platform': 'Win32',
            'cookies_enabled': True,
            'java_enabled': False,
            'flash_enabled': False
        }

    def _generate_browser_fingerprint(self, account_id: int) -> Dict:
        """为账号生成唯一的浏览器指纹"""
        # 使用账号ID作为随机种子，确保每个账号的指纹固定但唯一
        random.seed(account_id)

        # 常见的用户代理列表
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"
        ]

        # 常见的屏幕分辨率
        resolutions = [
            (1920, 1080), (1366, 768), (1536, 864), (1440, 900), (1280, 720)
        ]

        # WebGL渲染器
        webgl_renderers = [
            "ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)",
            "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0, D3D11)",
            "ANGLE (AMD, AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0, D3D11)"
        ]

        fingerprint = {
            'user_agent': random.choice(user_agents),
            'screen_resolution': random.choice(resolutions),
            'webgl_vendor': 'Google Inc. (Intel)',
            'webgl_renderer': random.choice(webgl_renderers),
            'timezone': random.choice(['America/New_York', 'America/Los_Angeles', 'America/Chicago']),
            'language': random.choice(['en-US', 'en-GB']),
            'platform': random.choice(['Win32', 'MacIntel']),
            'hardware_concurrency': random.choice([4, 8, 12, 16]),
            'device_memory': random.choice([4, 8, 16]),
            'canvas_fingerprint': f"canvas_{account_id}_{random.randint(1000, 9999)}",
            'audio_fingerprint': f"audio_{account_id}_{random.randint(1000, 9999)}"
        }

        # 重置随机种子
        random.seed()

        return fingerprint

    def _get_profile_path(self, account_id: int) -> str:
        """获取账号的浏览器配置文件路径"""
        profile_path = os.path.join(self.profiles_dir, f"account_{account_id}")
        if not os.path.exists(profile_path):
            os.makedirs(profile_path)
        return profile_path

    def _cleanup_browser(self, account_id: int):
        """清理指定账号的浏览器实例"""
        if account_id in self.active_browsers:
            try:
                browser = self.active_browsers[account_id]
                browser.quit()
            except:
                pass
            finally:
                del self.active_browsers[account_id]

        if account_id in self.browser_configs:
            del self.browser_configs[account_id]

    def create_browser(self, account_id: int, config: Dict = None,
                      headless: bool = None) -> webdriver.Chrome:
        """创建浏览器实例"""
        import threading
        import time
        
        # 使用锁来控制并发浏览器创建，避免资源竞争
        if not hasattr(self, '_browser_creation_lock'):
            self._browser_creation_lock = threading.Lock()
            
        with self._browser_creation_lock:
            try:
                # 检查是否已有活跃的浏览器实例
                if account_id in self.active_browsers:
                    existing_browser = self.active_browsers[account_id]
                    try:
                        # 检查浏览器是否仍然有效
                        existing_browser.current_url
                        self.bot_logger.log_account_action(
                            account_id, "未知用户", 'browser_reuse',
                            "重用现有浏览器实例"
                        )
                        return existing_browser
                    except:
                        # 浏览器已失效，清理并创建新的
                        self._cleanup_browser(account_id)
                
                # 限制并发浏览器数量，避免系统资源耗尽
                max_concurrent_browsers = 5
                if len(self.active_browsers) >= max_concurrent_browsers:
                    self.bot_logger.log_account_action(
                        account_id, "未知用户", 'browser_limit_reached',
                        f"已达到最大并发浏览器数量({max_concurrent_browsers})，等待其他浏览器释放"
                    )
                    # 等待一段时间后重试
                    time.sleep(2)
                    if len(self.active_browsers) >= max_concurrent_browsers:
                        raise Exception(f"系统浏览器数量已达上限({max_concurrent_browsers})，请稍后重试")

                # 获取账号信息
                account = self.db_manager.get_account(account_id)
                if not account:
                    raise ValueError(f"账号 {account_id} 不存在")

                # 获取持久化配置文件路径
                profile_path = self._get_profile_path(account_id)
                self.profile_paths[account_id] = profile_path

                # 生成浏览器指纹
                fingerprint = self._generate_browser_fingerprint(account_id)

                # 合并配置
                browser_config = self.default_config.copy()
                browser_config.update(fingerprint)  # 应用指纹
                if config:
                    browser_config.update(config)

                # 只有明确传入headless参数时才覆盖配置文件设置
                if headless is not None:
                    browser_config['headless'] = headless
                browser_config['profile_path'] = profile_path
            
                # 获取代理配置
                if account and account.get('proxy_id'):
                    proxy_info = self.db_manager.get_proxy_by_id(account['proxy_id'])
                    if proxy_info and proxy_info.get('status') == 'active':
                        browser_config['proxy'] = {
                            'host': proxy_info['host'],
                            'port': proxy_info['port'],
                            'username': proxy_info.get('username'),
                            'password': proxy_info.get('password'),
                            'type': proxy_info.get('type', 'http')
                        }
                
                # 根据代理IP设置时区和语言
                if browser_config.get('proxy'):
                    timezone, language = self._get_location_settings(
                        browser_config['proxy']
                    )
                    browser_config['timezone'] = timezone
                    browser_config['language'] = language
                
                # 创建浏览器 - 增加重试机制
                max_browser_retries = 2
                browser_created = False
                last_error = None
                
                for browser_attempt in range(max_browser_retries):
                    try:
                        if browser_config['browser_type'] == 'chrome':
                            driver = self._create_chrome_browser(browser_config)
                        else:
                            driver = self._create_firefox_browser(browser_config)
                        
                        browser_created = True
                        break
                        
                    except Exception as e:
                        last_error = e
                        self.bot_logger.log_account_action(
                            account_id, "未知用户", 'browser_creation_retry',
                            f"浏览器创建失败 (尝试 {browser_attempt + 1}/{max_browser_retries}): {e}"
                        )
                        
                        if browser_attempt < max_browser_retries - 1:
                            time.sleep(3)  # 等待后重试
                            self._cleanup_chrome_processes()  # 清理残留进程
                
                if not browser_created:
                    raise Exception(f"浏览器创建失败，已尝试{max_browser_retries}次: {last_error}")
                
                # 配置浏览器超时设置
                self._configure_browser_timeouts(driver, browser_config)
                
                # 保存浏览器实例
                self.active_browsers[account_id] = driver
                self.browser_configs[account_id] = browser_config

                # 注册到资源管理器
                from utils.resource_manager import resource_manager
                resource_manager.tracker.register(
                    f"browser_{account_id}",
                    driver,
                    lambda: self._safe_close_browser(driver, browser_config)
                )
                
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'browser_created',
                    f"创建浏览器实例成功，类型: {browser_config['browser_type']}"
                )
                
                return driver
                    
            except Exception as e:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'browser_error',
                    f"创建浏览器失败: {e}"
                )
                # 清理可能的残留资源
                self._cleanup_chrome_processes()
                raise
    
    def _create_chrome_browser(self, config: Dict) -> webdriver.Chrome:
        """创建Chrome浏览器"""
        options = ChromeOptions()
        
        # 基本设置
        if config['headless']:
            options.add_argument('--headless')
        
        # 优化的Chrome启动参数，减少崩溃风险
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-images')
        # options.add_argument('--disable-javascript')  # 注释掉，Twitter需要JavaScript
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-sync')
        options.add_argument('--disable-translate')
        options.add_argument('--hide-scrollbars')
        options.add_argument('--mute-audio')
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-logging')
        options.add_argument('--disable-gpu-logging')
        options.add_argument('--silent')
        options.add_argument('--log-level=3')
        options.add_experimental_option('excludeSwitches', ['enable-automation', 'enable-logging'])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 窗口大小
        width, height = config['window_size']
        options.add_argument(f'--window-size={width},{height}')
        
        # 用户代理
        if config.get('user_agent'):
            options.add_argument(f'--user-agent={config["user_agent"]}')
        
        # 代理设置
        if config.get('proxy'):
            proxy = config['proxy']
            if proxy['type'] == 'http':
                proxy_str = f"{proxy['host']}:{proxy['port']}"
                if proxy.get('username') and proxy.get('password'):
                    proxy_str = f"{proxy['username']}:{proxy['password']}@{proxy_str}"
                options.add_argument(f'--proxy-server=http://{proxy_str}')
            elif proxy['type'] == 'socks5':
                proxy_str = f"{proxy['host']}:{proxy['port']}"
                options.add_argument(f'--proxy-server=socks5://{proxy_str}')
        
        # 语言设置
        if config.get('language'):
            options.add_argument(f'--lang={config["language"]}')
            options.add_experimental_option('prefs', {
                'intl.accept_languages': config['language']
            })
        
        # 时区设置
        if config.get('timezone'):
            options.add_argument(f'--timezone={config["timezone"]}')
        
        # 使用持久化配置文件目录
        if config.get('profile_path'):
            user_data_dir = config['profile_path']
            options.add_argument(f'--user-data-dir={user_data_dir}')

            # 添加浏览器指纹参数
            if config.get('webgl_vendor'):
                options.add_argument(f'--webgl-vendor={config["webgl_vendor"]}')
            if config.get('webgl_renderer'):
                options.add_argument(f'--webgl-renderer={config["webgl_renderer"]}')
            if config.get('canvas_fingerprint'):
                options.add_argument(f'--canvas-fingerprint={config["canvas_fingerprint"]}')

            # 设置屏幕分辨率
            if config.get('screen_resolution'):
                width, height = config['screen_resolution']
                options.add_argument(f'--screen={width}x{height}')

            # 设置硬件并发数
            if config.get('hardware_concurrency'):
                options.add_argument(f'--max_old_space_size={config["hardware_concurrency"] * 512}')
        else:
            # 回退到临时目录
            from utils.resource_manager import resource_manager
            temp_context = resource_manager.temp_manager.temp_directory(prefix='chrome_profile_')
            user_data_dir = temp_context.__enter__()

        # 注册清理回调（仅对临时目录）
        if not config.get('profile_path'):
            def cleanup_user_data():
                try:
                    temp_context.__exit__(None, None, None)
                except Exception as e:
                    self.bot_logger.log_system_action('cleanup_error', f"清理用户数据目录失败: {e}")

            # 将清理回调与浏览器实例关联
            config['_cleanup_callback'] = cleanup_user_data
            options.add_argument(f'--user-data-dir={user_data_dir}')
        # 如果使用持久化配置文件，user-data-dir已经在上面设置了
        
        # 禁用图片加载（可选，提高速度）
        # options.add_experimental_option('prefs', {
        #     'profile.managed_default_content_settings.images': 2
        # })
        
        # 创建驱动 - 增强错误处理和重试机制
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                # 尝试使用webdriver-manager自动管理驱动
                try:
                    from webdriver_manager.chrome import ChromeDriverManager
                    from selenium.webdriver.chrome.service import Service
                    driver_path = ChromeDriverManager().install()
                    
                    # 修复webdriver-manager路径问题
                    if 'THIRD_PARTY_NOTICES' in driver_path or not driver_path.endswith('.exe'):
                        import os
                        driver_dir = os.path.dirname(driver_path)
                        driver_path = os.path.join(driver_dir, 'chromedriver.exe')
                        
                        # 验证修复后的路径是否存在
                        if not os.path.exists(driver_path):
                            raise Exception(f"ChromeDriver可执行文件不存在: {driver_path}")
                    
                    service = Service(driver_path)
                    driver = webdriver.Chrome(
                        service=service,
                        options=options
                    )
                except Exception as e:
                    self.bot_logger.log_system_action(
                        'webdriver_manager_error',
                        f"webdriver-manager失败: {e}，尝试使用系统PATH中的chromedriver"
                    )
                    # 如果webdriver-manager失败，尝试使用系统PATH中的chromedriver
                    driver = webdriver.Chrome(options=options)
                
                # 验证浏览器是否成功启动
                driver.get('data:text/html,<html><body>Browser Test</body></html>')
                
                # 执行反检测脚本
                self._execute_anti_detection_script(driver, config)
                
                self.bot_logger.log_system_action(
                    'chrome_browser_created',
                    f"Chrome浏览器创建成功，尝试次数: {attempt + 1}"
                )
                
                return driver
                
            except Exception as e:
                error_msg = str(e)
                
                # 分析错误类型并提供更详细的信息
                detailed_error = self._analyze_chrome_error(error_msg)
                
                self.bot_logger.log_system_action(
                    'chrome_browser_error',
                    f"Chrome浏览器创建失败 (尝试 {attempt + 1}/{max_retries}): {detailed_error}"
                )
                
                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries - 1:
                    import time
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                    
                    # 清理可能的残留进程
                    self._cleanup_chrome_processes()
                    
                    # 根据错误类型采取不同的修复措施
                    self._apply_error_fixes(error_msg, attempt)
                else:
                    # 最后一次尝试失败，抛出异常
                    raise Exception(f"Chrome浏览器创建失败，已尝试{max_retries}次: {detailed_error}\n\n解决建议:\n{self._get_error_suggestions(error_msg)}")
    
    def _create_firefox_browser(self, config: Dict) -> webdriver.Firefox:
        """创建Firefox浏览器"""
        options = FirefoxOptions()
        
        # 基本设置
        if config['headless']:
            options.add_argument('--headless')
        
        # 窗口大小
        width, height = config['window_size']
        options.add_argument(f'--width={width}')
        options.add_argument(f'--height={height}')
        
        # 代理设置
        if config.get('proxy'):
            proxy = config['proxy']
            profile = webdriver.FirefoxProfile()
            
            if proxy['type'] == 'http':
                profile.set_preference('network.proxy.type', 1)
                profile.set_preference('network.proxy.http', proxy['host'])
                profile.set_preference('network.proxy.http_port', proxy['port'])
                profile.set_preference('network.proxy.ssl', proxy['host'])
                profile.set_preference('network.proxy.ssl_port', proxy['port'])
            elif proxy['type'] == 'socks5':
                profile.set_preference('network.proxy.type', 1)
                profile.set_preference('network.proxy.socks', proxy['host'])
                profile.set_preference('network.proxy.socks_port', proxy['port'])
                profile.set_preference('network.proxy.socks_version', 5)
            
            options.profile = profile
        
        # 语言设置
        if config.get('language'):
            profile = options.profile or webdriver.FirefoxProfile()
            profile.set_preference('intl.accept_languages', config['language'])
            options.profile = profile
        
        # 创建驱动
        try:
            from webdriver_manager.firefox import GeckoDriverManager
            from selenium.webdriver.firefox.service import Service
            service = Service(GeckoDriverManager().install())
            driver = webdriver.Firefox(
                service=service,
                options=options
            )
        except Exception:
            # 如果webdriver-manager失败，尝试使用系统PATH中的geckodriver
            driver = webdriver.Firefox(options=options)
        
        return driver
    
    def _execute_anti_detection_script(self, driver: webdriver.Chrome, config: Dict):
        """执行反检测脚本"""
        try:
            # 隐藏webdriver属性
            driver.execute_script(
                "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
            )
            
            # 修改navigator属性
            if config.get('platform'):
                driver.execute_script(
                    f"Object.defineProperty(navigator, 'platform', {{get: () => '{config['platform']}'}});"
                )
            
            if config.get('hardware_concurrency'):
                driver.execute_script(
                    f"Object.defineProperty(navigator, 'hardwareConcurrency', {{get: () => {config['hardware_concurrency']}}})"
                )
            
            # 修改屏幕属性
            if config.get('screen_resolution'):
                width, height = config['screen_resolution']
                driver.execute_script(f"""
                    Object.defineProperty(screen, 'width', {{get: () => {width}}});
                    Object.defineProperty(screen, 'height', {{get: () => {height}}});
                    Object.defineProperty(screen, 'availWidth', {{get: () => {width}}});
                    Object.defineProperty(screen, 'availHeight', {{get: () => {height - 40}}});
                """)
            
            if config.get('color_depth'):
                driver.execute_script(
                    f"Object.defineProperty(screen, 'colorDepth', {{get: () => {config['color_depth']}}})"
                )
            
            if config.get('pixel_ratio'):
                driver.execute_script(
                    f"Object.defineProperty(window, 'devicePixelRatio', {{get: () => {config['pixel_ratio']}}})"
                )
            
            # WebGL指纹
            if config.get('webgl_vendor') and config.get('webgl_renderer'):
                driver.execute_script(f"""
                    const getParameter = WebGLRenderingContext.getParameter;
                    WebGLRenderingContext.prototype.getParameter = function(parameter) {{
                        if (parameter === 37445) {{
                            return '{config['webgl_vendor']}';
                        }}
                        if (parameter === 37446) {{
                            return '{config['webgl_renderer']}';
                        }}
                        return getParameter(parameter);
                    }};
                """)
            
            # Canvas指纹
            if config.get('canvas_fingerprint'):
                driver.execute_script("""
                    const toDataURL = HTMLCanvasElement.prototype.toDataURL;
                    HTMLCanvasElement.prototype.toDataURL = function(type) {
                        if (type === 'image/png') {
                            return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
                        }
                        return toDataURL.apply(this, arguments);
                    };
                """)
            
            # 音频指纹
            if config.get('audio_fingerprint'):
                driver.execute_script("""
                    const audioContext = window.AudioContext || window.webkitAudioContext;
                    if (audioContext) {
                        const originalCreateAnalyser = audioContext.prototype.createAnalyser;
                        audioContext.prototype.createAnalyser = function() {
                            const analyser = originalCreateAnalyser.apply(this, arguments);
                            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                            analyser.getFloatFrequencyData = function(array) {
                                const result = originalGetFloatFrequencyData.apply(this, arguments);
                                for (let i = 0; i < array.length; i++) {
                                    array[i] = array[i] + Math.random() * 0.0001;
                                }
                                return result;
                            };
                            return analyser;
                        };
                    }
                """)
            
        except Exception as e:
            self.bot_logger.log_system_action(
                'anti_detection_error',
                f"执行反检测脚本失败: {e}"
            )
    
    def _configure_browser_timeouts(self, driver, config: Dict):
        """配置浏览器超时设置"""
        try:
            # 从配置管理器获取超时设置
            from config.config_manager import ConfigManager
            config_manager = ConfigManager()
            browser_config = config_manager.get_browser_config()
            
            # 设置页面加载超时（默认30秒）
            page_load_timeout = getattr(browser_config, 'page_load_timeout', 30)
            driver.set_page_load_timeout(page_load_timeout)
            
            # 设置脚本执行超时（默认30秒）
            script_timeout = getattr(browser_config, 'script_timeout', 30)
            driver.set_script_timeout(script_timeout)
            
            # 设置隐式等待时间（默认10秒）
            implicit_wait = getattr(browser_config, 'implicit_wait', 10)
            driver.implicitly_wait(implicit_wait)
            
            self.bot_logger.log_system_action(
                'browser_timeouts_configured',
                f"浏览器超时设置完成 - 页面加载:{page_load_timeout}s, 脚本:{script_timeout}s, 隐式等待:{implicit_wait}s"
            )
            
        except Exception as e:
            self.bot_logger.log_system_action(
                'browser_timeout_error',
                f"配置浏览器超时失败: {e}"
            )
    
    def _get_location_settings(self, proxy: Dict) -> Tuple[str, str]:
        """根据代理IP获取时区和语言设置"""
        # 这里应该根据代理IP的地理位置来设置时区和语言
        # 简化实现，可以集成IP地理位置API
        
        # 默认设置
        timezone_map = {
            'US': 'America/New_York',
            'UK': 'Europe/London', 
            'DE': 'Europe/Berlin',
            'FR': 'Europe/Paris',
            'JP': 'Asia/Tokyo',
            'CN': 'Asia/Shanghai',
            'AU': 'Australia/Sydney'
        }
        
        language_map = {
            'US': 'en-US',
            'UK': 'en-GB',
            'DE': 'de-DE',
            'FR': 'fr-FR', 
            'JP': 'ja-JP',
            'CN': 'zh-CN',
            'AU': 'en-AU'
        }
        
        # 简单的IP地理位置检测（实际应用中应该使用专业的IP地理位置服务）
        country = 'US'  # 默认美国
        
        # 可以在这里添加IP地理位置检测逻辑
        # country = self._detect_country_by_ip(proxy['host'])
        
        timezone = timezone_map.get(country, 'America/New_York')
        language = language_map.get(country, 'en-US')
        
        return timezone, language
    
    def get_browser(self, account_id: int, auto_recover: bool = True) -> Optional[webdriver.Chrome]:
        """获取浏览器实例，增强会话验证和自动恢复 - 增强版"""
        if account_id not in self.active_browsers:
            return None
        
        driver = self.active_browsers[account_id]
        
        # 多层会话验证
        if not self._validate_browser_session(driver, account_id):
            if auto_recover:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'browser_auto_recover', "尝试自动恢复浏览器会话"
                )
                return self._recover_browser_session(account_id)
            else:
                self._cleanup_browser(account_id)
                return None
        
        return driver
    
    def _validate_browser_session(self, driver, account_id: int) -> bool:
        """多层浏览器会话验证"""
        validation_checks = [
            ('basic_connection', self._check_basic_connection),
            ('page_responsiveness', self._check_page_responsiveness),
            ('dom_accessibility', self._check_dom_accessibility),
            ('javascript_execution', self._check_javascript_execution)
        ]
        
        failed_checks = []
        
        for check_name, check_func in validation_checks:
            try:
                if not check_func(driver):
                    failed_checks.append(check_name)
            except Exception as e:
                failed_checks.append(f"{check_name}({type(e).__name__})")
        
        if failed_checks:
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'session_validation_failed',
                f"会话验证失败: {', '.join(failed_checks)}"
            )
            return False
        
        return True
    
    def _check_basic_connection(self, driver) -> bool:
        """检查基本连接"""
        try:
            _ = driver.current_url
            return True
        except Exception:
            return False
    
    def _check_page_responsiveness(self, driver) -> bool:
        """检查页面响应性"""
        try:
            # 检查页面加载状态
            ready_state = driver.execute_script("return document.readyState")
            return ready_state in ['complete', 'interactive']
        except Exception:
            return False
    
    def _check_dom_accessibility(self, driver) -> bool:
        """检查DOM可访问性"""
        try:
            # 尝试访问基本DOM元素
            _ = driver.find_element(By.TAG_NAME, 'body')
            return True
        except Exception:
            return False
    
    def _check_javascript_execution(self, driver) -> bool:
        """检查JavaScript执行能力"""
        try:
            # 执行简单的JavaScript测试
            result = driver.execute_script("return 'test_' + Date.now()")
            return result and result.startswith('test_')
        except Exception:
            return False
    
    def _recover_browser_session(self, account_id: int, max_attempts: int = 2) -> Optional[webdriver.Chrome]:
        """自动恢复浏览器会话"""
        for attempt in range(max_attempts):
            try:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'session_recovery_attempt',
                    f"会话恢复尝试 {attempt + 1}/{max_attempts}"
                )
                
                # 清理当前会话
                self._cleanup_browser(account_id)
                
                # 获取原始配置
                original_config = self.browser_configs.get(account_id, {})
                
                # 重新创建浏览器
                new_driver = self.create_browser(
                    account_id=account_id,
                    config=original_config,
                    headless=original_config.get('headless', True)
                )
                
                if new_driver and self._validate_browser_session(new_driver, account_id):
                    self.bot_logger.log_account_action(
                        account_id, "未知用户", 'session_recovery_success',
                        f"会话恢复成功 (尝试 {attempt + 1})"
                    )
                    return new_driver
                
            except Exception as e:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'session_recovery_error',
                    f"会话恢复失败 (尝试 {attempt + 1}): {e}"
                )
                
                # 短暂延迟后重试
                if attempt < max_attempts - 1:
                    time.sleep(2)
        
        self.bot_logger.log_account_action(
            account_id, "未知用户", 'session_recovery_failed',
            f"所有会话恢复尝试均失败"
        )
        return None
    
    def _analyze_chrome_error(self, error_msg: str) -> str:
        """分析Chrome错误并提供详细信息"""
        error_msg_lower = error_msg.lower()
        
        if 'chromedriver' in error_msg_lower and 'executable' in error_msg_lower:
            return f"ChromeDriver可执行文件问题: {error_msg}\n可能原因: ChromeDriver未安装或路径不正确"
        elif 'session not created' in error_msg_lower:
            return f"Chrome会话创建失败: {error_msg}\n可能原因: Chrome版本与ChromeDriver版本不匹配"
        elif 'chrome failed to start' in error_msg_lower or 'chrome crashed' in error_msg_lower:
            return f"Chrome启动失败: {error_msg}\n可能原因: Chrome进程崩溃或系统资源不足"
        elif 'timeout' in error_msg_lower:
            return f"Chrome启动超时: {error_msg}\n可能原因: 系统负载过高或Chrome启动缓慢"
        elif 'permission' in error_msg_lower or 'access' in error_msg_lower:
            return f"权限问题: {error_msg}\n可能原因: 用户数据目录权限不足或被占用"
        elif 'port' in error_msg_lower:
            return f"端口问题: {error_msg}\n可能原因: Chrome调试端口被占用"
        else:
            return f"未知错误: {error_msg}"
    
    def _apply_error_fixes(self, error_msg: str, attempt: int):
        """根据错误类型应用修复措施"""
        error_msg_lower = error_msg.lower()
        
        try:
            if 'chromedriver' in error_msg_lower:
                # ChromeDriver问题，尝试重新安装
                self.bot_logger.log_system_action(
                    'chrome_fix_attempt', f"尝试修复ChromeDriver问题 (第{attempt + 1}次)"
                )
                self._fix_chromedriver_issue()
            
            elif 'session not created' in error_msg_lower or 'version' in error_msg_lower:
                # 版本不匹配问题
                self.bot_logger.log_system_action(
                    'chrome_fix_attempt', f"尝试修复版本匹配问题 (第{attempt + 1}次)"
                )
                self._fix_version_mismatch()
            
            elif 'permission' in error_msg_lower or 'access' in error_msg_lower:
                # 权限问题
                self.bot_logger.log_system_action(
                    'chrome_fix_attempt', f"尝试修复权限问题 (第{attempt + 1}次)"
                )
                self._fix_permission_issue()
            
            elif 'port' in error_msg_lower:
                # 端口问题
                self.bot_logger.log_system_action(
                    'chrome_fix_attempt', f"尝试修复端口问题 (第{attempt + 1}次)"
                )
                self._fix_port_issue()
                
        except Exception as e:
            self.bot_logger.log_system_action(
                'chrome_fix_error', f"应用错误修复失败: {e}"
            )
    
    def _get_error_suggestions(self, error_msg: str) -> str:
        """获取错误解决建议"""
        error_msg_lower = error_msg.lower()
        suggestions = []
        
        if 'chromedriver' in error_msg_lower:
            suggestions.extend([
                "1. 检查ChromeDriver是否正确安装",
                "2. 运行 'pip install --upgrade webdriver-manager' 更新webdriver-manager",
                "3. 手动下载匹配的ChromeDriver版本"
            ])
        
        if 'session not created' in error_msg_lower or 'version' in error_msg_lower:
            suggestions.extend([
                "1. 更新Chrome浏览器到最新版本",
                "2. 清理webdriver-manager缓存",
                "3. 重新安装ChromeDriver"
            ])
        
        if 'permission' in error_msg_lower or 'access' in error_msg_lower:
            suggestions.extend([
                "1. 以管理员权限运行程序",
                "2. 检查用户数据目录权限",
                "3. 清理browser_profiles目录"
            ])
        
        if 'port' in error_msg_lower:
            suggestions.extend([
                "1. 重启计算机释放端口",
                "2. 检查是否有其他Chrome实例在运行",
                "3. 使用任务管理器结束Chrome进程"
            ])
        
        if not suggestions:
            suggestions = [
                "1. 重启计算机",
                "2. 重新安装Chrome浏览器",
                "3. 检查系统资源使用情况",
                "4. 运行fix_chrome_issues.py修复脚本"
            ]
        
        return "\n".join(suggestions)
    
    def _fix_chromedriver_issue(self):
        """修复ChromeDriver问题"""
        try:
            # 清理webdriver-manager缓存
            import shutil
            from pathlib import Path
            
            cache_dir = Path.home() / '.wdm'
            if cache_dir.exists():
                shutil.rmtree(cache_dir)
                self.bot_logger.log_system_action(
                    'chrome_fix', "已清理webdriver-manager缓存"
                )
        except Exception as e:
            self.bot_logger.log_system_action(
                'chrome_fix_error', f"清理webdriver-manager缓存失败: {e}"
            )
    
    def _fix_version_mismatch(self):
        """修复版本不匹配问题"""
        try:
            # 强制重新下载ChromeDriver
            self._fix_chromedriver_issue()
        except Exception as e:
            self.bot_logger.log_system_action(
                'chrome_fix_error', f"修复版本不匹配失败: {e}"
            )
    
    def _fix_permission_issue(self):
        """修复权限问题"""
        try:
            # 清理可能被锁定的用户数据目录
            import shutil
            import tempfile
            
            # 创建新的临时目录
            temp_dir = tempfile.mkdtemp(prefix='chrome_temp_')
            self.bot_logger.log_system_action(
                'chrome_fix', f"创建临时用户数据目录: {temp_dir}"
            )
        except Exception as e:
            self.bot_logger.log_system_action(
                'chrome_fix_error', f"修复权限问题失败: {e}"
            )
    
    def _fix_port_issue(self):
        """修复端口问题"""
        try:
            # 清理Chrome进程
            self._cleanup_chrome_processes()
            
            # 等待端口释放
            import time
            time.sleep(2)
            
            self.bot_logger.log_system_action(
                'chrome_fix', "已清理Chrome进程并等待端口释放"
            )
        except Exception as e:
            self.bot_logger.log_system_action(
                'chrome_fix_error', f"修复端口问题失败: {e}"
            )
    
    def _cleanup_chrome_processes(self):
        """清理可能的Chrome残留进程"""
        try:
            import psutil
            import os
            
            # 查找并终止Chrome相关进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        # 检查是否是我们启动的Chrome进程
                        if proc.info['cmdline'] and any('--user-data-dir' in arg for arg in proc.info['cmdline']):
                            proc.terminate()
                            proc.wait(timeout=3)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    pass
                    
        except Exception as e:
            self.bot_logger.log_system_action(
                'chrome_cleanup_error', f"清理Chrome进程失败: {e}"
            )
    
    def _safe_close_browser(self, driver, config: Dict = None):
        """安全关闭浏览器实例"""
        try:
            if driver:
                driver.quit()

            # 执行清理回调
            if config and '_cleanup_callback' in config:
                config['_cleanup_callback']()
                
            # 额外清理Chrome进程
            self._cleanup_chrome_processes()

        except Exception as e:
            self.bot_logger.log_system_action(
                'browser_cleanup_error', f"浏览器清理失败: {e}"
            )

    def close_browser(self, account_id: int):
        """关闭浏览器实例"""
        if account_id in self.active_browsers:
            try:
                driver = self.active_browsers[account_id]
                config = self.browser_configs.get(account_id, {})

                # 从资源管理器注销
                from utils.resource_manager import resource_manager
                resource_manager.tracker.unregister(f"browser_{account_id}")

                # 安全关闭浏览器
                self._safe_close_browser(driver, config)

                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'browser_closed', "关闭浏览器实例"
                )

            except Exception as e:
                self.bot_logger.log_account_action(
                    account_id, "未知用户", 'browser_error',
                    f"关闭浏览器失败: {e}"
                )
            finally:
                # 清理本地引用
                if account_id in self.active_browsers:
                    del self.active_browsers[account_id]
                if account_id in self.browser_configs:
                    del self.browser_configs[account_id]
    
    def close_all_browsers(self):
        """关闭所有浏览器实例"""
        account_ids = list(self.active_browsers.keys())
        for account_id in account_ids:
            self.close_browser(account_id)
    
    def is_browser_active(self, account_id: int) -> bool:
        """检查浏览器是否活跃"""
        if account_id not in self.active_browsers:
            return False
        
        try:
            driver = self.active_browsers[account_id]
            # 尝试获取当前URL来检查浏览器是否还活跃
            driver.current_url
            return True
        except Exception:
            # 浏览器已关闭或出错
            self.close_browser(account_id)
            return False
    
    def navigate_to_twitter(self, account_id: int) -> bool:
        """导航到Twitter"""
        driver = self.get_browser(account_id)
        if not driver:
            return False
        
        try:
            driver.get('https://x.com')
            
            # 等待页面加载
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, 'body'))
            )
            
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'navigate_twitter', "导航到Twitter"
            )
            
            return True
            
        except Exception as e:
            self.bot_logger.log_account_action(
                account_id, "未知用户", 'navigate_error',
                f"导航到Twitter失败: {e}"
            )
            return False
    
    def wait_for_element(self, driver: webdriver.Chrome, by: By, value: str,
                        timeout: int = 5):
        """等待元素出现 - 优化超时时间"""
        return WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((by, value))
        )

    def _filter_text_for_chrome(self, text: str) -> str:
        """过滤ChromeDriver不支持的字符"""
        import unicodedata
        
        # 支持的表情符号白名单（扩展版本，包含更多常用表情符号）
        supported_emojis = {
            # 基本符号和表情（在BMP范围内）
            '☺', '☻', '♠', '♣', '♥', '♦', '♪', '♫', '☀', '☁', '☂', '☃', '☄',
            '★', '☆', '☎', '☏', '☐', '☑', '☒', '☓', '☔', '☕', '☘', '☙', '☚', '☛',
            '☜', '☝', '☞', '☟', '☠', '☡', '☢', '☣', '☤', '☥', '☦', '☧', '☨', '☩',
            '☪', '☫', '☬', '☭', '☮', '☯', '☰', '☱', '☲', '☳', '☴', '☵', '☶', '☷',
            '☸', '☹', '☺', '☻', '☼', '☽', '☾', '☿', '♀', '♁', '♂', '♃', '♄', '♅',
            '♆', '♇', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓',
            '♔', '♕', '♖', '♗', '♘', '♙', '♚', '♛', '♜', '♝', '♞', '♟', '♠', '♡',
            '♢', '♣', '♤', '♥', '♦', '♧', '♨', '♩', '♪', '♫', '♬', '♭', '♮', '♯',
            # 常用表情符号（通过Unicode转义序列支持）
            '😀', '😁', '😂', '😃', '😄', '😅', '😆', '😇', '😈', '😉', '😊', '😋',
            '😌', '😍', '😎', '😏', '😐', '😑', '😒', '😓', '😔', '😕', '😖', '😗',
            '😘', '😙', '😚', '😛', '😜', '😝', '😞', '😟', '😠', '😡', '😢', '😣',
            '😤', '😥', '😦', '😧', '😨', '😩', '😪', '😫', '😬', '😭', '😮', '😯',
            '😰', '😱', '😲', '😳', '😴', '😵', '😶', '😷', '😸', '😹', '😺', '😻',
            '😼', '😽', '😾', '😿', '🙀', '🙁', '🙂', '🙃', '🙄', '🙅', '🙆', '🙇',
            '🙈', '🙉', '🙊', '🙋', '🙌', '🙍', '🙎', '🙏',
            # 手势和身体部位
            '👀', '👁', '👂', '👃', '👄', '👅', '👆', '👇', '👈', '👉', '👊', '👋',
            '👌', '👍', '👎', '👏', '👐', '👑', '👒', '👓', '👔', '👕', '👖', '👗',
            '👘', '👙', '👚', '👛', '👜', '👝', '👞', '👟', '👠', '👡', '👢', '👣',
            '👤', '👥', '👦', '👧', '👨', '👩', '👪', '👫', '👬', '👭', '👮', '👯',
            '👰', '👱', '👲', '👳', '👴', '👵', '👶', '👷', '👸', '👹', '👺', '👻',
            '👼', '👽', '👾', '👿', '💀', '💁', '💂', '💃', '💄', '💅', '💆', '💇',
            '💈', '💉', '💊', '💋', '💌', '💍', '💎', '💏', '💐', '💑', '💒', '💓',
            '💔', '💕', '💖', '💗', '💘', '💙', '💚', '💛', '💜', '💝', '💞', '💟',
            '💠', '💡', '💢', '💣', '💤', '💥', '💦', '💧', '💨', '💩', '💪', '💫',
            '💬', '💭', '💮', '💯', '💰', '💱', '💲', '💳', '💴', '💵', '💶', '💷',
            '💸', '💹', '💺', '💻', '💼', '💽', '💾', '💿', '📀', '📁', '📂', '📃',
            # 自然和动物
            '🌀', '🌁', '🌂', '🌃', '🌄', '🌅', '🌆', '🌇', '🌈', '🌉', '🌊', '🌋',
            '🌌', '🌍', '🌎', '🌏', '🌐', '🌑', '🌒', '🌓', '🌔', '🌕', '🌖', '🌗',
            '🌘', '🌙', '🌚', '🌛', '🌜', '🌝', '🌞', '🌟', '🌠', '🌡', '🌢', '🌣',
            '🌤', '🌥', '🌦', '🌧', '🌨', '🌩', '🌪', '🌫', '🌬', '🌭', '🌮', '🌯',
            '🌰', '🌱', '🌲', '🌳', '🌴', '🌵', '🌶', '🌷', '🌸', '🌹', '🌺', '🌻',
            '🌼', '🌽', '🌾', '🌿', '🍀', '🍁', '🍂', '🍃', '🍄', '🍅', '🍆', '🍇',
            '🍈', '🍉', '🍊', '🍋', '🍌', '🍍', '🍎', '🍏', '🍐', '🍑', '🍒', '🍓',
            '🍔', '🍕', '🍖', '🍗', '🍘', '🍙', '🍚', '🍛', '🍜', '🍝', '🍞', '🍟',
            '🍠', '🍡', '🍢', '🍣', '🍤', '🍥', '🍦', '🍧', '🍨', '🍩', '🍪', '🍫',
            '🍬', '🍭', '🍮', '🍯', '🍰', '🍱', '🍲', '🍳', '🍴', '🍵', '🍶', '🍷',
            '🍸', '🍹', '🍺', '🍻', '🍼', '🍽', '🍾', '🍿', '🎀', '🎁', '🎂', '🎃',
            # 活动和物品
            '🎄', '🎅', '🎆', '🎇', '🎈', '🎉', '🎊', '🎋', '🎌', '🎍', '🎎', '🎏',
            '🎐', '🎑', '🎒', '🎓', '🎔', '🎕', '🎖', '🎗', '🎘', '🎙', '🎚', '🎛',
            '🎜', '🎝', '🎞', '🎟', '🎠', '🎡', '🎢', '🎣', '🎤', '🎥', '🎦', '🎧',
            '🎨', '🎩', '🎪', '🎫', '🎬', '🎭', '🎮', '🎯', '🎰', '🎱', '🎲', '🎳',
            '🎴', '🎵', '🎶', '🎷', '🎸', '🎹', '🎺', '🎻', '🎼', '🎽', '🎾', '🎿',
            '🏀', '🏁', '🏂', '🏃', '🏄', '🏅', '🏆', '🏇', '🏈', '🏉', '🏊', '🏋',
            '🏌', '🏍', '🏎', '🏏', '🏐', '🏑', '🏒', '🏓', '🏔', '🏕', '🏖', '🏗',
            '🏘', '🏙', '🏚', '🏛', '🏜', '🏝', '🏞', '🏟', '🏠', '🏡', '🏢', '🏣',
            '🏤', '🏥', '🏦', '🏧', '🏨', '🏩', '🏪', '🏫', '🏬', '🏭', '🏮', '🏯',
            '🏰', '🏱', '🏲', '🏳', '🏴', '🏵', '🏶', '🏷', '🏸', '🏹', '🏺', '🏻',
            '🏼', '🏽', '🏾', '🏿'
        }
        
        filtered_chars = []
        for char in text:
            # 检查字符是否在BMP范围内 (U+0000 到 U+FFFF)
            if ord(char) <= 0xFFFF:
                filtered_chars.append(char)
            elif char in supported_emojis:
                # 如果是支持的表情符号，保留它
                filtered_chars.append(char)
                self.bot_logger.log_system_action(
                    'emoji_supported',
                    f"保留支持的表情符号: {char} (U+{ord(char):04X})"
                )
            else:
                # 尝试将字符标准化为兼容形式
                try:
                    normalized = unicodedata.normalize('NFKC', char)
                    if normalized and ord(normalized[0]) <= 0xFFFF:
                        filtered_chars.append(normalized)
                    else:
                        # 如果无法标准化，跳过该字符
                        self.bot_logger.log_system_action(
                            'character_filter',
                            f"跳过不支持的字符: {char} (U+{ord(char):04X})"
                        )
                except Exception:
                    # 如果处理失败，跳过该字符
                    pass
        
        filtered_text = ''.join(filtered_chars)
        
        if len(filtered_text) != len(text):
            self.bot_logger.log_system_action(
                'text_filter',
                f"文本已过滤: 原长度 {len(text)}, 过滤后长度 {len(filtered_text)}"
            )
        
        return filtered_text

    def simulate_human_typing(self, element, text: str):
        """模拟人类输入"""
        import time
        import random

        try:
            # 清空输入框
            element.clear()
            time.sleep(0.2)

            # 过滤文本
            filtered_text = self._filter_text_for_chrome(text)
            
            if len(filtered_text) != len(text):
                self.bot_logger.log_system_action(
                    'text_filtering',
                    f"文本过滤: 原长度 {len(text)} -> 过滤后长度 {len(filtered_text)}"
                )

            # 如果过滤后文本为空，记录警告
            if not filtered_text.strip():
                self.bot_logger.log_system_action(
                    'typing_warning',
                    f"过滤后文本为空，原文本: '{text[:50]}...'"
                )
                return

            # 逐字符输入，模拟人类打字速度
            successful_chars = 0
            failed_chars = 0
            
            for i, char in enumerate(filtered_text):
                try:
                    element.send_keys(char)
                    successful_chars += 1
                    # 随机延迟，模拟真实打字速度
                    time.sleep(random.uniform(0.05, 0.15))
                    
                    # 每输入10个字符检查一次元素状态
                    if i % 10 == 0 and i > 0:
                        try:
                            # 检查元素是否仍然可用
                            element.is_enabled()
                        except Exception:
                            self.bot_logger.log_system_action(
                                'typing_element_lost',
                                f"输入元素失效，已输入 {successful_chars} 个字符"
                            )
                            break
                            
                except Exception as e:
                    failed_chars += 1
                    # 如果单个字符输入失败，记录并跳过
                    self.bot_logger.log_system_action(
                        'typing_error',
                        f"输入字符失败: {char} (U+{ord(char):04X}), 错误: {e}"
                    )
                    continue
            
            # 记录输入统计
            self.bot_logger.log_system_action(
                'typing_complete',
                f"输入完成 - 成功: {successful_chars}, 失败: {failed_chars}, 总计: {len(filtered_text)}"
            )
            
        except Exception as e:
            self.bot_logger.log_system_action(
                'typing_fatal_error',
                f"输入过程发生严重错误: {e}"
            )

    def simulate_human_click(self, element):
        """模拟人类点击"""
        import time
        import random
        from selenium.webdriver.common.action_chains import ActionChains

        # 移动到元素并点击
        actions = ActionChains(element.parent)
        actions.move_to_element(element)
        actions.pause(random.uniform(0.1, 0.3))
        actions.click()
        actions.perform()

        # 点击后短暂延迟
        time.sleep(random.uniform(0.1, 0.3))
    
    def wait_for_clickable(self, driver: webdriver.Chrome, by: By, value: str,
                          timeout: int = 5):
        """等待元素可点击 - 优化超时时间"""
        return WebDriverWait(driver, timeout).until(
            EC.element_to_be_clickable((by, value))
        )
    
    def safe_click(self, driver: webdriver.Chrome, element, delay: float = None):
        """安全点击元素"""
        try:
            # 滚动到元素位置
            driver.execute_script("arguments[0].scrollIntoView(true);", element)
            
            # 随机延迟
            if delay is None:
                delay = random.uniform(0.5, 2.0)
            time.sleep(delay)
            
            # 使用ActionChains模拟人类点击
            actions = ActionChains(driver)
            actions.move_to_element(element)
            actions.pause(random.uniform(0.1, 0.3))
            actions.click()
            actions.perform()
            
            return True
            
        except Exception as e:
            # 如果ActionChains失败，尝试直接点击
            try:
                element.click()
                return True
            except Exception:
                return False
    
    def safe_send_keys(self, driver: webdriver.Chrome, element, text: str, 
                      typing_delay: float = None):
        """安全输入文本"""
        try:
            # 清空输入框
            element.clear()
            
            # 使用相同的字符过滤逻辑
            filtered_text = self._filter_text_for_chrome(text)
            
            # 模拟人类打字
            if typing_delay is None:
                typing_delay = random.uniform(0.05, 0.15)
            
            for char in filtered_text:
                try:
                    element.send_keys(char)
                    time.sleep(typing_delay + random.uniform(-0.02, 0.02))
                except Exception as e:
                    # 如果单个字符输入失败，记录并跳过
                    self.bot_logger.log_system_action(
                        'safe_typing_error',
                        f"安全输入字符失败: {char} (U+{ord(char):04X}), 错误: {e}"
                    )
                    continue
            
            return True
            
        except Exception:
            return False
    
    def random_scroll(self, driver: webdriver.Chrome, direction: str = 'down',
                     distance: int = None):
        """随机滚动页面"""
        try:
            if distance is None:
                distance = random.randint(200, 800)
            
            if direction == 'down':
                driver.execute_script(f"window.scrollBy(0, {distance});")
            elif direction == 'up':
                driver.execute_script(f"window.scrollBy(0, -{distance});")
            
            # 随机停顿
            time.sleep(random.uniform(1.0, 3.0))
            
        except Exception:
            pass
    
    def simulate_human_behavior(self, driver: webdriver.Chrome):
        """模拟人类行为"""
        try:
            # 随机移动鼠标
            actions = ActionChains(driver)
            
            # 获取窗口大小
            window_size = driver.get_window_size()
            width = window_size['width']
            height = window_size['height']
            
            # 随机移动鼠标到不同位置
            for _ in range(random.randint(2, 5)):
                x = random.randint(0, width)
                y = random.randint(0, height)
                
                actions.move_by_offset(x - width//2, y - height//2)
                actions.pause(random.uniform(0.5, 1.5))
            
            actions.perform()
            
            # 随机滚动
            if random.choice([True, False]):
                self.random_scroll(driver)
            
        except Exception:
            pass
    
    def get_browser_stats(self) -> Dict:
        """获取浏览器统计信息"""
        active_count = len(self.active_browsers)
        
        browser_types = {}
        for config in self.browser_configs.values():
            browser_type = config.get('browser_type', 'unknown')
            browser_types[browser_type] = browser_types.get(browser_type, 0) + 1
        
        return {
            'active_browsers': active_count,
            'browser_types': browser_types,
            'total_created': len(self.browser_configs)
        }
    
    def cleanup_inactive_browsers(self):
        """清理不活跃的浏览器"""
        inactive_accounts = []
        
        for account_id in list(self.active_browsers.keys()):
            if not self.is_browser_active(account_id):
                inactive_accounts.append(account_id)
        
        for account_id in inactive_accounts:
            self.close_browser(account_id)
        
        if inactive_accounts:
            self.bot_logger.log_system_action(
                'cleanup_browsers',
                f"清理了 {len(inactive_accounts)} 个不活跃的浏览器实例"
            )
        
        return len(inactive_accounts)