#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试并发互动修复
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.browser_manager import get_safe_event_loop


class MockAccount:
    """模拟账号类"""
    def __init__(self, id, username, email, password, cookies, proxy, status):
        self.id = id
        self.username = username
        self.email = email
        self.password = password
        self.cookies = cookies
        self.proxy = proxy
        self.status = status


async def test_concurrent_interaction_fix():
    """测试并发互动修复"""
    print("🧪 测试并发互动修复...")
    
    try:
        # 创建测试账号
        test_accounts = [
            MockAccount(
                id=1,
                username="test_account_1",
                email="<EMAIL>",
                password="test_password",
                cookies="[]",
                proxy="",
                status="active"
            ),
            MockAccount(
                id=2,
                username="test_account_2",
                email="<EMAIL>",
                password="test_password",
                cookies="[]",
                proxy="",
                status="active"
            ),
            MockAccount(
                id=3,
                username="test_account_3",
                email="<EMAIL>",
                password="test_password",
                cookies="[]",
                proxy="",
                status="active"
            )
        ]
        
        # 测试事件循环获取
        print("🔧 测试事件循环获取...")
        loop1 = get_safe_event_loop()
        loop2 = get_safe_event_loop()
        print(f"   - 循环1: {id(loop1)}")
        print(f"   - 循环2: {id(loop2)}")
        print(f"   - 是否相同: {loop1 is loop2}")
        
        # 跳过互动执行器创建，只测试事件循环
        
        # 准备互动任务
        tasks = []
        post_url = "https://x.com/test/status/*********"
        
        for account in test_accounts:
            tasks.append((account, "like", None))
        
        print(f"🚀 准备执行 {len(tasks)} 个并发互动任务...")
        
        # 测试并发执行（模拟）
        try:
            # 这里我们只测试任务创建和事件循环，不实际执行浏览器操作
            semaphore = asyncio.Semaphore(3)
            
            async def mock_interaction_task(task_info):
                """模拟互动任务"""
                account, action_type, comment_content = task_info
                
                async with semaphore:
                    try:
                        # 模拟延迟
                        await asyncio.sleep(0.1)
                        
                        print(f"   🎯 模拟互动: {account.username} - {action_type}")
                        
                        # 测试事件循环一致性
                        current_loop = get_safe_event_loop()
                        print(f"      - 任务循环: {id(current_loop)}")
                        
                        return {
                            'account_id': account.id,
                            'account_username': account.username,
                            'action_type': action_type,
                            'success': True
                        }
                        
                    except Exception as e:
                        print(f"   ❌ 模拟任务失败: {account.username} - {e}")
                        return {
                            'account_id': account.id,
                            'account_username': account.username,
                            'action_type': action_type,
                            'success': False,
                            'error': str(e)
                        }
            
            # 创建并发任务
            current_loop = get_safe_event_loop()
            concurrent_tasks = []
            
            for task in tasks:
                coro = mock_interaction_task(task)
                task_obj = current_loop.create_task(coro)
                concurrent_tasks.append(task_obj)
            
            print(f"   📋 创建了 {len(concurrent_tasks)} 个任务")
            
            # 执行并发任务
            results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
            
            # 处理结果
            success_count = 0
            error_count = 0
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    print(f"   ❌ 任务 {i+1} 异常: {result}")
                    error_count += 1
                else:
                    if result.get('success'):
                        print(f"   ✅ 任务 {i+1} 成功: {result['account_username']}")
                        success_count += 1
                    else:
                        print(f"   ❌ 任务 {i+1} 失败: {result['account_username']}")
                        error_count += 1
            
            print(f"\n📊 测试结果:")
            print(f"   - 成功: {success_count}")
            print(f"   - 失败: {error_count}")
            print(f"   - 总计: {len(results)}")
            
            if error_count == 0:
                print("✅ 并发互动修复测试通过！")
                return True
            else:
                print("❌ 并发互动修复测试失败！")
                return False
                
        except Exception as e:
            print(f"❌ 并发测试异常: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试初始化失败: {e}")
        return False


async def main():
    """主函数"""
    print("🧪 开始并发互动修复测试...")
    
    success = await test_concurrent_interaction_fix()
    
    if success:
        print("\n🎉 所有测试通过！")
        return 0
    else:
        print("\n💥 测试失败！")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试运行失败: {e}")
        sys.exit(1)
